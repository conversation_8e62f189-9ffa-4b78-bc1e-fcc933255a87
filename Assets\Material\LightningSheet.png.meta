fileFormatVersion: 2
guid: 9685c3a024e22a946a7e8c2ce3542300
AssetOrigin:
  serializedVersion: 1
  productId: 158988
  packageName: Sprite Shaders Ultimate
  packageVersion: 6.16
  assetPath: Assets/Sprite Shaders Ultimate/Textures/Patterns/LightningSheet.png
  uploadId: 764641
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 4096
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 100
    crunchedCompression: 1
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: LightningSheet_0
      rect:
        serializedVersion: 2
        x: 0
        y: 2730.6665
        width: 1365.3334
        height: 1365.3334
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 194c8417710ec3548a7564f61a527f79
      internalID: 476662083
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: LightningSheet_1
      rect:
        serializedVersion: 2
        x: 1365
        y: 2730.6665
        width: 1365.3334
        height: 1365.3334
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6cb86ab624aaca54e8da65f0a62977e7
      internalID: -1273111232
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: LightningSheet_2
      rect:
        serializedVersion: 2
        x: 2730
        y: 2730.6665
        width: 1365.3334
        height: 1365.3334
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 33abfa21fe0629946954eeab7306dce0
      internalID: -1944961663
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: LightningSheet_3
      rect:
        serializedVersion: 2
        x: 0
        y: 1365.6666
        width: 1365.3334
        height: 1365.3334
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7647be450fff1c141857bc81dcc3e0ac
      internalID: 1809370299
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: LightningSheet_4
      rect:
        serializedVersion: 2
        x: 1365
        y: 1365.6666
        width: 1365.3334
        height: 1365.3334
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 432aa5763d37f094d8dd51156be3473b
      internalID: 780220408
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: LightningSheet_5
      rect:
        serializedVersion: 2
        x: 2730
        y: 1365.6666
        width: 1365.3334
        height: 1365.3334
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ac199764d0f5b494ab9857ef2404706f
      internalID: -527170009
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: LightningSheet_6
      rect:
        serializedVersion: 2
        x: 0
        y: 0.666626
        width: 1365.3334
        height: 1365.3334
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f0e4054b5a3504148a4e50e106b55515
      internalID: -1493359570
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: LightningSheet_7
      rect:
        serializedVersion: 2
        x: 1365
        y: 0.666626
        width: 1365.3334
        height: 1365.3334
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d52d5ed82d106394b991d6923be663c1
      internalID: -1026905385
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: LightningSheet_8
      rect:
        serializedVersion: 2
        x: 2730
        y: 0.666626
        width: 1365.3334
        height: 1365.3334
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 972a366f59daa7c4a89e4cca35f81067
      internalID: 1088011745
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      LightningSheet_0: 476662083
      LightningSheet_1: -1273111232
      LightningSheet_2: -1944961663
      LightningSheet_3: 1809370299
      LightningSheet_4: 780220408
      LightningSheet_5: -527170009
      LightningSheet_6: -1493359570
      LightningSheet_7: -1026905385
      LightningSheet_8: 1088011745
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
