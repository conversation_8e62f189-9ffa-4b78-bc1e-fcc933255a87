%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d3d0b52bf8c3e684f96ec4333fba0f67, type: 3}
  m_Name: Spell Echo
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Items.SupportGemData
  icon: {fileID: 21300000, guid: 6f482212d7793854497e30b878434e7e, type: 3}
  backgroundIcon: {fileID: 0}
  foregroundIcon: {fileID: 0}
  gemName: Spell Echo
  description: Supports spell skills, making them repeat when cast.
  rarity: 2
  useIntegerScaling: 1
  commonStatMultiplier: 0.7
  uncommonStatMultiplier: 0.85
  rareStatMultiplier: 1
  epicStatMultiplier: 1.15
  uniqueStatMultiplier: 1.3
  commonIntMultiplier: 1
  uncommonIntMultiplier: 2
  rareIntMultiplier: 3
  epicIntMultiplier: 4
  uniqueIntMultiplier: 5
  compatibleTags: 4
  damageIncreased: 0
  damageMore: 1
  cooldownMultiplier: 1
  manaCostMultiplier: 1
  attackSpeedMultiplier: 1
  addedCritChance: 0
  critMultiplierModifier: 1
  addsPierce: 0
  addsChain: 0
  addsAreaDamage: 0
  addsMultipleProjectiles: 0
  extraProjectiles: 2
  projectileSpreadAngle: 15
  chainCount: 2
  areaRadius: 3
  addsSpellEcho: 1
  echoDelay: 0.4
  echoCount: 1
  echoSpreadRadius: 0
  addsFork: 0
  forkCount: 2
  forkAngle: 30
  igniteEffectivenessMultiplier: 1
  igniteDurationMultiplier: 1
  freezeEffectivenessMultiplier: 1
  freezeDurationMultiplier: 1
  bleedEffectivenessMultiplier: 1
  bleedDurationMultiplier: 1
  shockEffectivenessMultiplier: 1
  shockRangeMultiplier: 1
