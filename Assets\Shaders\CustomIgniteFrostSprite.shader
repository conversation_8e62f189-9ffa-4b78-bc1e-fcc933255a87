Shader "Custom/IgniteFrostSprite"
{
    Properties
    {
        // Main Texture
        _MainTex("Sprite Texture", 2D) = "white" {}
        _Color("Tint", Color) = (1,1,1,1)
        
        // Spritesheet Support
        [Toggle(_SPRITESHEETFIX_ON)] _SpriteSheetFix("Sprite Sheet Fix", Float) = 0
        _SpriteSheetRect("Sprite Sheet Rect", Vector) = (0,0,1,1)
        
        // Lighting
        _MaskMap("Mask Map", 2D) = "white" {}
        _NormalMap("Normal Map", 2D) = "bump" {}
        _NormalIntensity("Normal Intensity", Float) = 1
        
        // Noise
        _NoiseTexture("Noise Texture", 2D) = "white" {}
        
        // Ignite Effect
        [Header(Ignite Effect)]
        [Toggle(_ENABLE_IGNITE)] _EnableIgnite("Enable Ignite", Float) = 0
        _IgniteAmount("Ignite Amount", Range(0, 1)) = 0
        _IgniteOutlineWidth("Ignite Outline Width", Range(0, 0.1)) = 0.02
        _IgniteDissolveAmount("Ignite Dissolve Amount", Range(0, 0.5)) = 0.1
        _IgniteWaveFrequency("Ignite Wave Frequency", Float) = 10
        _IgniteWaveSpeed("Ignite Wave Speed", Float) = 3
        [HDR]_IgniteColorInner("Ignite Color Inner", Color) = (5.992157, 2.5, 0, 1)
        [HDR]_IgniteColorOuter("Ignite Color Outer", Color) = (2.996078, 0.5, 0, 1)
        _IgniteNoiseScale("Ignite Noise Scale", Float) = 5
        _IgniteNoiseSpeed("Ignite Noise Speed", Float) = 2
        
        // Lightning Effect
        [Header(Lightning Effect)]
        [Toggle(_ENABLE_LIGHTNING)] _EnableLightning("Enable Lightning", Float) = 0
        _LightningAmount("Lightning Amount", Range(0, 1)) = 0
        _LightningTexture("Lightning Texture", 2D) = "white" {}
        _LightningScale("Lightning Scale", Float) = 1
        _LightningSpeed("Lightning Speed", Float) = 5
        _LightningFrequency("Lightning Frequency", Float) = 10
        _LightningIntensity("Lightning Intensity", Float) = 2
        [HDR]_LightningColor("Lightning Color", Color) = (0.5, 0.8, 1, 1)
        _LightningOffset("Lightning Offset Speed", Vector) = (0.1, 0.15, 0, 0)
        
        // Frost Effect
        [Header(Frost Effect)]
        [Toggle(_ENABLE_FROST)] _EnableFrost("Enable Frost", Float) = 0
        _FrostAmount("Frost Amount", Range(0, 1)) = 0
        _FrostTint("Frost Tint", Color) = (0.7, 0.9, 1, 1)
        _FrostContrast("Frost Contrast", Float) = 1.5
        _IceRoughness("Ice Roughness", Range(0, 1)) = 0.5
        _FrostRimPower("Frost Rim Power", Float) = 2
        [HDR]_FrostRimColor("Frost Rim Color", Color) = (1.5, 2, 2.5, 1)
        _FrostNoiseScale("Frost Noise Scale", Float) = 5
        _FrostHighlightDensity("Frost Highlight Density", Float) = 0.5
        [HDR]_FrostHighlightColor("Frost Highlight Color", Color) = (2, 2.5, 3, 1)
        
        // Renderer settings
        [HideInInspector] _RendererColor("RendererColor", Color) = (1,1,1,1)
        [HideInInspector] _Flip("Flip", Vector) = (1,1,1,1)
        [HideInInspector] _AlphaTex("External Alpha", 2D) = "white" {}
        [HideInInspector] _EnableExternalAlpha("Enable External Alpha", Float) = 0
    }

    SubShader
    {
        Tags 
        {
            "Queue" = "Transparent"
            "RenderType" = "Transparent"
            "RenderPipeline" = "UniversalPipeline"
            "PreviewType" = "Plane"
            "CanUseSpriteAtlas" = "True"
        }

        Blend SrcAlpha OneMinusSrcAlpha, One OneMinusSrcAlpha
        Cull Off
        ZWrite Off

        Pass
        {
            Name "Sprite Lit"
            Tags { "LightMode" = "Universal2D" }
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile _ _ENABLE_IGNITE
            #pragma multi_compile _ _ENABLE_FROST
            #pragma multi_compile _ _ENABLE_LIGHTNING
            #pragma multi_compile _ _SPRITESHEETFIX_ON
            #pragma multi_compile _ USE_SHAPE_LIGHT_TYPE_0
            #pragma multi_compile _ USE_SHAPE_LIGHT_TYPE_1
            #pragma multi_compile _ USE_SHAPE_LIGHT_TYPE_2
            #pragma multi_compile _ USE_SHAPE_LIGHT_TYPE_3
            #pragma multi_compile _ DEBUG_DISPLAY

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/Shaders/2D/Include/LightingUtility.hlsl"

            struct Attributes
            {
                float3 positionOS   : POSITION;
                float4 color        : COLOR;
                float2 uv           : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4 positionCS   : SV_POSITION;
                half4 color         : COLOR;
                float2 uv           : TEXCOORD0;
                half2 lightingUV    : TEXCOORD1;
                float3 positionWS   : TEXCOORD2;
                float3 positionOS   : TEXCOORD3;  // Object space position for stable effects
                UNITY_VERTEX_OUTPUT_STEREO
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            TEXTURE2D(_MaskMap);
            SAMPLER(sampler_MaskMap);
            TEXTURE2D(_NormalMap);
            SAMPLER(sampler_NormalMap);
            TEXTURE2D(_NoiseTexture);
            SAMPLER(sampler_NoiseTexture);
            TEXTURE2D(_LightningTexture);
            SAMPLER(sampler_LightningTexture);
            
            CBUFFER_START(UnityPerMaterial)
                float4 _MainTex_ST;
                half4 _Color;
                half4 _RendererColor;
                float _NormalIntensity;
                float4 _SpriteSheetRect;
                
                // Ignite
                float _IgniteAmount;
                float _IgniteOutlineWidth;
                float _IgniteDissolveAmount;
                float _IgniteWaveFrequency;
                float _IgniteWaveSpeed;
                half4 _IgniteColorInner;
                half4 _IgniteColorOuter;
                float _IgniteNoiseScale;
                float _IgniteNoiseSpeed;
                
                // Lightning
                float _LightningAmount;
                float _LightningScale;
                float _LightningSpeed;
                float _LightningFrequency;
                float _LightningIntensity;
                half4 _LightningColor;
                float4 _LightningOffset;
                
                // Frost
                float _FrostAmount;
                half4 _FrostTint;
                float _FrostContrast;
                float _IceRoughness;
                float _FrostRimPower;
                half4 _FrostRimColor;
                float _FrostNoiseScale;
                float _FrostHighlightDensity;
                half4 _FrostHighlightColor;
            CBUFFER_END

            #if USE_SHAPE_LIGHT_TYPE_0
            SHAPE_LIGHT(0)
            #endif

            #if USE_SHAPE_LIGHT_TYPE_1
            SHAPE_LIGHT(1)
            #endif

            #if USE_SHAPE_LIGHT_TYPE_2
            SHAPE_LIGHT(2)
            #endif

            #if USE_SHAPE_LIGHT_TYPE_3
            SHAPE_LIGHT(3)
            #endif

            Varyings vert(Attributes v)
            {
                Varyings o = (Varyings)0;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

                o.positionOS = v.positionOS;  // Pass object space position
                o.positionWS = TransformObjectToWorld(v.positionOS);
                o.positionCS = TransformWorldToHClip(o.positionWS);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.lightingUV = half2(ComputeScreenPos(o.positionCS / o.positionCS.w).xy);
                o.color = v.color * _Color * _RendererColor;
                
                return o;
            }

            #include "Packages/com.unity.render-pipelines.universal/Shaders/2D/Include/CombinedShapeLightShared.hlsl"

            half3 ApplyFrostEffect(half3 color, float2 uv, float3 objectPos, float alpha)
            {
                #ifdef _ENABLE_FROST
                if (_FrostAmount > 0)
                {
                    // Use object space position for stable noise sampling
                    float2 stableUV = objectPos.xy;
                    
                    // Sample noise for frost pattern using stable coordinates
                    float2 noiseUV = stableUV * _FrostNoiseScale;
                    float noise = SAMPLE_TEXTURE2D(_NoiseTexture, sampler_NoiseTexture, noiseUV).r;
                    
                    // Create frost tint
                    half3 frostColor = lerp(color, color * _FrostTint.rgb, _FrostAmount);
                    
                    // Add contrast
                    frostColor = saturate(pow(frostColor, _FrostContrast));
                    
                    // Ice highlights
                    float highlight = pow(noise, 3) * _FrostHighlightDensity;
                    frostColor += _FrostHighlightColor.rgb * highlight * _FrostAmount;
                    
                    // Rim frost effect
                    float rim = 1.0 - saturate(alpha);
                    rim = pow(rim, _FrostRimPower);
                    frostColor += _FrostRimColor.rgb * rim * _FrostAmount * 0.5;
                    
                    color = lerp(color, frostColor, _FrostAmount);
                }
                #endif
                return color;
            }

            float SampleAlphaAroundUV(float2 uv, float outlineWidth)
            {
                float alpha = 0;
                const int samples = 8;
                
                // Scale outline width by spritesheet size to maintain consistent width
                #ifdef _SPRITESHEETFIX_ON
                    float2 scaledWidth = outlineWidth / _SpriteSheetRect.zw;
                #else
                    float2 scaledWidth = float2(outlineWidth, outlineWidth);
                #endif
                
                for (int i = 0; i < samples; i++)
                {
                    float angle = (i / float(samples)) * 6.28318;
                    float2 offset = float2(cos(angle) * scaledWidth.x, sin(angle) * scaledWidth.y);
                    alpha = max(alpha, SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, uv + offset).a);
                }
                
                return alpha;
            }

            half4 ApplyIgniteEffect(half4 color, float2 uv, float3 objectPos, float time)
            {
                #ifdef _ENABLE_IGNITE
                if (_IgniteAmount > 0)
                {
                    // Sample the texture to get original alpha
                    float originalAlpha = color.a;
                    
                    // Create outline by sampling around the current pixel
                    float outlineAlpha = SampleAlphaAroundUV(uv, _IgniteOutlineWidth);
                    
                    // Detect edge pixels (where outline exists but original doesn't)
                    float isEdge = saturate(outlineAlpha - originalAlpha);
                    
                    if (isEdge > 0 || originalAlpha > 0.01)
                    {
                        // Use object space position for stable effect coordinates
                        float2 stableUV = objectPos.xy;
                        
                        // Create animated wave pattern using stable coordinates
                        float wave = sin(stableUV.y * _IgniteWaveFrequency + time * _IgniteWaveSpeed) * 0.5 + 0.5;
                        wave += sin(stableUV.x * _IgniteWaveFrequency * 0.7 + time * _IgniteWaveSpeed * 1.3) * 0.5 + 0.5;
                        wave *= 0.5;
                        
                        // Sample noise for dissolve effect using stable coordinates
                        float2 noiseUV = stableUV * _IgniteNoiseScale + time * _IgniteNoiseSpeed * 0.1;
                        float noise = SAMPLE_TEXTURE2D(_NoiseTexture, sampler_NoiseTexture, noiseUV).r;
                        
                        // Combine wave and noise for dissolve pattern
                        float dissolvePattern = (wave * 0.6 + noise * 0.4);
                        
                        // Calculate distance from edge
                        float edgeDistance = saturate(originalAlpha);
                        
                        // Create dissolve mask that affects the outline
                        // Remap so that 0 = no effect, 1 = full effect
                        float threshold = 1.0 - _IgniteAmount;
                        float dissolveMask = saturate((dissolvePattern - threshold) / _IgniteDissolveAmount);
                        
                        // Apply dissolve to the edge
                        if (isEdge > 0)
                        {
                            // Flame colors on the outline - inverted lerp so outer color is visible
                            half3 flameColor = lerp(_IgniteColorInner.rgb, _IgniteColorOuter.rgb, dissolveMask);
                            color.rgb = flameColor * dissolveMask * _IgniteAmount;
                            color.a = dissolveMask * _IgniteAmount;
                        }
                        else
                        {
                            // For the main sprite, add some inner glow
                            float innerGlow = (1.0 - edgeDistance) * dissolveMask * 0.5;
                            color.rgb += _IgniteColorInner.rgb * innerGlow * _IgniteAmount;
                        }
                        
                        // Add extra glow on the burning edges
                        if (dissolveMask > 0.3 && dissolveMask < 0.7)
                        {
                            color.rgb += _IgniteColorInner.rgb * 0.5 * _IgniteAmount;
                        }
                    }
                }
                #endif
                return color;
            }

            half4 ApplyLightningEffect(half4 color, float2 uv, float3 objectPos, float time)
            {
                #ifdef _ENABLE_LIGHTNING
                if (_LightningAmount > 0)
                {
                    // Create pulsing effect
                    float pulse = sin(time * _LightningFrequency) * 0.5 + 0.5;
                    pulse = pow(pulse, 2); // Make it more dramatic
                    
                    // Sample lightning texture with animated offset
                    float2 lightningUV1 = objectPos.xy * _LightningScale + _LightningOffset.xy * time * _LightningSpeed;
                    float2 lightningUV2 = objectPos.xy * _LightningScale * 0.7 - _LightningOffset.xy * time * _LightningSpeed * 1.3;
                    
                    float lightning1 = SAMPLE_TEXTURE2D(_LightningTexture, sampler_LightningTexture, lightningUV1).r;
                    float lightning2 = SAMPLE_TEXTURE2D(_LightningTexture, sampler_LightningTexture, lightningUV2).r;
                    
                    // Combine lightning samples
                    float lightningMask = max(lightning1, lightning2);
                    
                    // Apply threshold to make it more dramatic
                    lightningMask = saturate((lightningMask - 0.5) * 2);
                    
                    // Flicker effect
                    float flicker = sin(time * _LightningSpeed * 20) * 0.1 + 0.9;
                    
                    // Apply lightning
                    half3 lightningColor = _LightningColor.rgb * _LightningIntensity * pulse * flicker;
                    color.rgb += lightningColor * lightningMask * _LightningAmount * color.a;
                    
                    // Add slight overall tint when shocked
                    color.rgb = lerp(color.rgb, color.rgb * _LightningColor.rgb, _LightningAmount * 0.2 * pulse);
                }
                #endif
                return color;
            }

            half4 frag(Varyings i) : SV_Target
            {
                float time = _Time.y;
                
                // Apply spritesheet fix if enabled
                float2 finalUV = i.uv;
                #ifdef _SPRITESHEETFIX_ON
                    finalUV = (i.uv - _SpriteSheetRect.xy) / _SpriteSheetRect.zw;
                #endif
                
                // Sample main texture
                half4 mainTex = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, finalUV);
                half4 mask = SAMPLE_TEXTURE2D(_MaskMap, sampler_MaskMap, finalUV);
                
                // Apply vertex color
                mainTex *= i.color;
                
                // Apply Frost effect first
                mainTex.rgb = ApplyFrostEffect(mainTex.rgb, finalUV, i.positionOS, mainTex.a);
                
                // Apply Ignite effect
                mainTex = ApplyIgniteEffect(mainTex, finalUV, i.positionOS, time);
                
                // Apply Lightning effect on top
                mainTex = ApplyLightningEffect(mainTex, finalUV, i.positionOS, time);
                
                // Setup lighting
                SurfaceData2D surfaceData;
                InputData2D inputData;
                
                InitializeSurfaceData(mainTex.rgb, mainTex.a, mask, surfaceData);
                InitializeInputData(finalUV, i.lightingUV, inputData);
                
                // Apply lighting
                return CombinedShapeLightShared(surfaceData, inputData);
            }
            ENDHLSL
        }

        Pass
        {
            Name "Sprite Normal"
            Tags { "LightMode" = "NormalsRendering"}

            HLSLPROGRAM
            #pragma vertex NormalsRenderingVertex
            #pragma fragment NormalsRenderingFragment

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/Shaders/2D/Include/NormalsRenderingShared.hlsl"

            struct Attributes
            {
                float3 positionOS   : POSITION;
                float4 color        : COLOR;
                float2 uv           : TEXCOORD0;
                float4 tangent      : TANGENT;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4 positionCS   : SV_POSITION;
                half4 color         : COLOR;
                float2 uv           : TEXCOORD0;
                half3 normalWS      : TEXCOORD1;
                half3 tangentWS     : TEXCOORD2;
                half3 bitangentWS   : TEXCOORD3;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            TEXTURE2D(_NormalMap);
            SAMPLER(sampler_NormalMap);
            
            float4 _NormalMap_ST;
            float _NormalIntensity;

            Varyings NormalsRenderingVertex(Attributes attributes)
            {
                Varyings o = (Varyings)0;
                UNITY_SETUP_INSTANCE_ID(attributes);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

                o.positionCS = TransformObjectToHClip(attributes.positionOS);
                o.uv = TRANSFORM_TEX(attributes.uv, _NormalMap);
                o.color = attributes.color;
                o.normalWS = -GetViewForwardDir();
                o.tangentWS = TransformObjectToWorldDir(attributes.tangent.xyz);
                o.bitangentWS = cross(o.normalWS, o.tangentWS) * attributes.tangent.w;
                return o;
            }

            half4 NormalsRenderingFragment(Varyings i) : SV_Target
            {
                const half4 mainTex = i.color * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, i.uv);
                const half3 normalTS = UnpackNormalScale(SAMPLE_TEXTURE2D(_NormalMap, sampler_NormalMap, i.uv), _NormalIntensity);
                return NormalsRenderingShared(mainTex, normalTS, i.tangentWS.xyz, i.bitangentWS.xyz, i.normalWS.xyz);
            }
            ENDHLSL
        }

        Pass
        {
            Name "Sprite Forward"
            Tags { "LightMode" = "UniversalForward" }

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile _ _ENABLE_IGNITE
            #pragma multi_compile _ _ENABLE_FROST
            #pragma multi_compile _ _ENABLE_LIGHTNING
            #pragma multi_compile _ _SPRITESHEETFIX_ON

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            struct Attributes
            {
                float3 positionOS   : POSITION;
                float4 color        : COLOR;
                float2 uv           : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4 positionCS   : SV_POSITION;
                half4 color         : COLOR;
                float2 uv           : TEXCOORD0;
                float3 positionOS   : TEXCOORD1;  // Object space position for stable effects
                UNITY_VERTEX_OUTPUT_STEREO
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            TEXTURE2D(_NoiseTexture);
            SAMPLER(sampler_NoiseTexture);
            TEXTURE2D(_LightningTexture);
            SAMPLER(sampler_LightningTexture);
            
            float4 _MainTex_ST;
            half4 _Color;
            half4 _RendererColor;
            float4 _SpriteSheetRect;
            
            // Include effect parameters
            float _IgniteAmount;
            float _IgniteOutlineWidth;
            float _IgniteDissolveAmount;
            float _IgniteWaveFrequency;
            float _IgniteWaveSpeed;
            half4 _IgniteColorInner;
            half4 _IgniteColorOuter;
            float _IgniteNoiseScale;
            float _IgniteNoiseSpeed;
            
            // Lightning  
            float _LightningAmount;
            float _LightningScale;
            float _LightningSpeed;
            float _LightningFrequency;
            float _LightningIntensity;
            half4 _LightningColor;
            float4 _LightningOffset;
            
            float _FrostAmount;
            half4 _FrostTint;
            float _FrostContrast;
            float _IceRoughness;
            float _FrostRimPower;
            half4 _FrostRimColor;
            float _FrostNoiseScale;
            float _FrostHighlightDensity;
            half4 _FrostHighlightColor;

            Varyings vert(Attributes attributes)
            {
                Varyings o = (Varyings)0;
                UNITY_SETUP_INSTANCE_ID(attributes);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

                o.positionOS = attributes.positionOS;  // Pass object space position
                o.positionCS = TransformObjectToHClip(attributes.positionOS);
                o.uv = TRANSFORM_TEX(attributes.uv, _MainTex);
                o.color = attributes.color * _Color * _RendererColor;
                return o;
            }

            half3 ApplyFrostEffect(half3 color, float2 uv, float3 objectPos, float alpha)
            {
                #ifdef _ENABLE_FROST
                if (_FrostAmount > 0)
                {
                    // Use object space position for stable noise sampling
                    float2 stableUV = objectPos.xy;
                    float2 noiseUV = stableUV * _FrostNoiseScale;
                    float noise = SAMPLE_TEXTURE2D(_NoiseTexture, sampler_NoiseTexture, noiseUV).r;
                    
                    half3 frostColor = lerp(color, color * _FrostTint.rgb, _FrostAmount);
                    frostColor = saturate(pow(frostColor, _FrostContrast));
                    
                    float highlight = pow(noise, 3) * _FrostHighlightDensity;
                    frostColor += _FrostHighlightColor.rgb * highlight * _FrostAmount;
                    
                    float rim = 1.0 - saturate(alpha);
                    rim = pow(rim, _FrostRimPower);
                    frostColor += _FrostRimColor.rgb * rim * _FrostAmount * 0.5;
                    
                    color = lerp(color, frostColor, _FrostAmount);
                }
                #endif
                return color;
            }

            float SampleAlphaAroundUV(float2 uv, float outlineWidth)
            {
                float alpha = 0;
                const int samples = 8;
                
                // Scale outline width by spritesheet size to maintain consistent width
                #ifdef _SPRITESHEETFIX_ON
                    float2 scaledWidth = outlineWidth / _SpriteSheetRect.zw;
                #else
                    float2 scaledWidth = float2(outlineWidth, outlineWidth);
                #endif
                
                for (int i = 0; i < samples; i++)
                {
                    float angle = (i / float(samples)) * 6.28318;
                    float2 offset = float2(cos(angle) * scaledWidth.x, sin(angle) * scaledWidth.y);
                    alpha = max(alpha, SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, uv + offset).a);
                }
                
                return alpha;
            }

            half4 ApplyIgniteEffect(half4 color, float2 uv, float3 objectPos, float time)
            {
                #ifdef _ENABLE_IGNITE
                if (_IgniteAmount > 0)
                {
                    // Sample the texture to get original alpha
                    float originalAlpha = color.a;
                    
                    // Create outline by sampling around the current pixel
                    float outlineAlpha = SampleAlphaAroundUV(uv, _IgniteOutlineWidth);
                    
                    // Detect edge pixels (where outline exists but original doesn't)
                    float isEdge = saturate(outlineAlpha - originalAlpha);
                    
                    if (isEdge > 0 || originalAlpha > 0.01)
                    {
                        // Use object space position for stable effect coordinates
                        float2 stableUV = objectPos.xy;
                        
                        // Create animated wave pattern using stable coordinates
                        float wave = sin(stableUV.y * _IgniteWaveFrequency + time * _IgniteWaveSpeed) * 0.5 + 0.5;
                        wave += sin(stableUV.x * _IgniteWaveFrequency * 0.7 + time * _IgniteWaveSpeed * 1.3) * 0.5 + 0.5;
                        wave *= 0.5;
                        
                        // Sample noise for dissolve effect using stable coordinates
                        float2 noiseUV = stableUV * _IgniteNoiseScale + time * _IgniteNoiseSpeed * 0.1;
                        float noise = SAMPLE_TEXTURE2D(_NoiseTexture, sampler_NoiseTexture, noiseUV).r;
                        
                        // Combine wave and noise for dissolve pattern
                        float dissolvePattern = (wave * 0.6 + noise * 0.4);
                        
                        // Calculate distance from edge
                        float edgeDistance = saturate(originalAlpha);
                        
                        // Create dissolve mask that affects the outline
                        // Remap so that 0 = no effect, 1 = full effect
                        float threshold = 1.0 - _IgniteAmount;
                        float dissolveMask = saturate((dissolvePattern - threshold) / _IgniteDissolveAmount);
                        
                        // Apply dissolve to the edge
                        if (isEdge > 0)
                        {
                            // Flame colors on the outline - inverted lerp so outer color is visible
                            half3 flameColor = lerp(_IgniteColorInner.rgb, _IgniteColorOuter.rgb, dissolveMask);
                            color.rgb = flameColor * dissolveMask * _IgniteAmount;
                            color.a = dissolveMask * _IgniteAmount;
                        }
                        else
                        {
                            // For the main sprite, add some inner glow
                            float innerGlow = (1.0 - edgeDistance) * dissolveMask * 0.5;
                            color.rgb += _IgniteColorInner.rgb * innerGlow * _IgniteAmount;
                        }
                        
                        // Add extra glow on the burning edges
                        if (dissolveMask > 0.3 && dissolveMask < 0.7)
                        {
                            color.rgb += _IgniteColorInner.rgb * 0.5 * _IgniteAmount;
                        }
                    }
                }
                #endif
                return color;
            }

            half4 ApplyLightningEffect(half4 color, float2 uv, float3 objectPos, float time)
            {
                #ifdef _ENABLE_LIGHTNING
                if (_LightningAmount > 0)
                {
                    // Create pulsing effect
                    float pulse = sin(time * _LightningFrequency) * 0.5 + 0.5;
                    pulse = pow(pulse, 2); // Make it more dramatic
                    
                    // Sample lightning texture with animated offset
                    float2 lightningUV1 = objectPos.xy * _LightningScale + _LightningOffset.xy * time * _LightningSpeed;
                    float2 lightningUV2 = objectPos.xy * _LightningScale * 0.7 - _LightningOffset.xy * time * _LightningSpeed * 1.3;
                    
                    float lightning1 = SAMPLE_TEXTURE2D(_LightningTexture, sampler_LightningTexture, lightningUV1).r;
                    float lightning2 = SAMPLE_TEXTURE2D(_LightningTexture, sampler_LightningTexture, lightningUV2).r;
                    
                    // Combine lightning samples
                    float lightningMask = max(lightning1, lightning2);
                    
                    // Apply threshold to make it more dramatic
                    lightningMask = saturate((lightningMask - 0.5) * 2);
                    
                    // Flicker effect
                    float flicker = sin(time * _LightningSpeed * 20) * 0.1 + 0.9;
                    
                    // Apply lightning
                    half3 lightningColor = _LightningColor.rgb * _LightningIntensity * pulse * flicker;
                    color.rgb += lightningColor * lightningMask * _LightningAmount * color.a;
                    
                    // Add slight overall tint when shocked
                    color.rgb = lerp(color.rgb, color.rgb * _LightningColor.rgb, _LightningAmount * 0.2 * pulse);
                }
                #endif
                return color;
            }

            half4 frag(Varyings i) : SV_Target
            {
                float time = _Time.y;
                
                // Apply spritesheet fix if enabled
                float2 finalUV = i.uv;
                #ifdef _SPRITESHEETFIX_ON
                    finalUV = (i.uv - _SpriteSheetRect.xy) / _SpriteSheetRect.zw;
                #endif
                
                half4 mainTex = i.color * SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, finalUV);
                
                mainTex.rgb = ApplyFrostEffect(mainTex.rgb, finalUV, i.positionOS, mainTex.a);
                mainTex = ApplyIgniteEffect(mainTex, finalUV, i.positionOS, time);
                mainTex = ApplyLightningEffect(mainTex, finalUV, i.positionOS, time);
                
                return mainTex;
            }
            ENDHLSL
        }
    }

    Fallback "Sprites/Default"
}