using UnityEngine;

/// <summary>
/// Interface for items that can be selected in the selection UI (gems, buffs, etc.)
/// </summary>
public interface ISelectable
{
    string GetDisplayName();
    string GetFormattedDescription();
    Sprite GetIcon();
    Color GetRarityColor();
    SelectionType GetSelectionType();
}

public enum SelectionType
{
    SkillGem,
    SupportGem,
    PlayerBuff
}

/// <summary>
/// Wrapper for GemInstance to implement ISelectable
/// </summary>
public class SelectableGem : ISelectable
{
    private GemInstance gem;
    
    public SelectableGem(GemInstance gemInstance)
    {
        gem = gemInstance;
    }
    
    public GemInstance GetGem() => gem;
    
    public string GetDisplayName() => gem.DisplayName;
    
    public string GetFormattedDescription()
    {
        string description = "";
        
        // Gem Type with color
        if (gem.IsSkillGem)
        {
            description += "<color=#CC3333><b>Skill Gem</b></color>\n";
        }
        else if (gem.IsSupportGem)
        {
            description += "<color=#33CC33><b>Support Gem</b></color>\n";
        }
        
        description += "\n";
        
        // Description
        if (gem.gemDataTemplate != null && !string.IsNullOrEmpty(gem.gemDataTemplate.description))
        {
            description += $"{gem.gemDataTemplate.description}\n\n";
        }
        
        // Stats
        description += "<size=110%><b><color=#FFD700>Stats</color></b></size>\n";
        description += GetFormattedGemStats();
        
        return description;
    }
    
    private string GetFormattedGemStats()
    {
        if (gem.IsSkillGem && gem.gemDataTemplate is SkillGemData skillGem)
        {
            string stats = $"<color=#CCCCCC>Damage:</color> <b>{gem.GetSkillDamage():F0} ({skillGem.damageType})</b>\n" +
                          $"<color=#CCCCCC>Cooldown:</color> <b>{gem.GetSkillCooldown():F1}s</b>\n" +
                          $"<color=#CCCCCC>Mana Cost:</color> <b>{gem.GetSkillManaCost():F0}</b>\n" +
                          $"<color=#CCCCCC>Support Slots:</color> <b>{gem.GetSupportSlotCount()}</b>";

            // Add status effect information if ailment chance > 0
            if (skillGem.ailmentChance > 0)
            {
                stats += $"\n\n<color=#FFD700>Status Effects ({skillGem.ailmentChance:F0}% chance):</color>";

                switch (skillGem.damageType)
                {
                    case DamageType.Fire:
                        float totalIgniteDamage = skillGem.ignitePercent * 100f;
                        stats += $"\n<color=#FF6B35>• Ignite:</color> {totalIgniteDamage:F0}% damage over {skillGem.igniteDuration:F1}s";
                        break;

                    case DamageType.Ice:
                        float slowPercent = skillGem.freezeSlowAmount * 100f;
                        stats += $"\n<color=#4FC3F7>• Freeze:</color> {slowPercent:F0}% slow for {skillGem.freezeDuration:F1}s";
                        break;

                    case DamageType.Physical:
                        float totalBleedDamage = skillGem.bleedPercent * 100f;
                        stats += $"\n<color=#8B0000>• Bleed:</color> {totalBleedDamage:F0}% damage over {skillGem.bleedDuration:F1}s";
                        break;

                    case DamageType.Lightning:
                        float chainPercent = skillGem.shockChainDamage * 100f;
                        stats += $"\n<color=#FFE082>• Shock:</color> {chainPercent:F0}% chain damage, {skillGem.shockChainRange:F1}m range for {skillGem.shockDuration:F1}s";
                        break;
                }
            }

            return stats;
        }
        else if (gem.IsSupportGem && gem.gemDataTemplate is SupportGemData supportGem)
        {
            string stats = "";
            
            if (supportGem.damageIncreased != 0f)
                stats += $"<color=#90EE90>{supportGem.damageIncreased:+0;-0}% increased Damage</color>\n";
                
            if (supportGem.damageMore != 1f)
                stats += $"<color=#FFD700>{(supportGem.damageMore - 1f) * 100:+0;-0}% more Damage</color>\n";
            
            if (supportGem.cooldownMultiplier != 1f)
                stats += $"<color=#CCCCCC>Cooldown:</color> <b>{(supportGem.cooldownMultiplier - 1f) * 100:+0;-0}%</b>\n";
            
            if (supportGem.manaCostMultiplier != 1f)
                stats += $"<color=#CCCCCC>Mana Cost:</color> <b>{(supportGem.manaCostMultiplier - 1f) * 100:+0;-0}%</b>\n";
            
            if (supportGem.attackSpeedMultiplier != 1f)
                stats += $"<color=#CCCCCC>Attack Speed:</color> <b>{(supportGem.attackSpeedMultiplier - 1f) * 100:+0;-0}%</b>\n";
            
            if (supportGem.addedCritChance != 0f)
                stats += $"<color=#CCCCCC>Crit Chance:</color> <b>+{supportGem.addedCritChance:F1}%</b>\n";
            
            if (supportGem.critMultiplierModifier != 1f)
                stats += $"<color=#CCCCCC>Crit Multiplier:</color> <b>{(supportGem.critMultiplierModifier - 1f) * 100:+0;-0}%</b>\n";
            
            // Special effects
            if (supportGem.addsPierce)
                stats += "<b>Projectiles Pierce</b>\n";
            
            if (supportGem.addsChain)
                stats += $"<b>Chain +{supportGem.chainCount} times</b>\n";
            
            if (supportGem.addsAreaDamage)
                stats += $"<b>Area Damage (radius: {supportGem.areaRadius})</b>\n";
            
            if (supportGem.addsFork)
                stats += $"<b>Fork into {gem.GetForkCount()} projectiles ({supportGem.forkAngle}° spread)</b>\n";

            if (supportGem.addsMultipleProjectiles)
            {
                stats += $"<b>+{gem.GetExtraProjectiles()} Projectiles";
                if (supportGem.projectileSpreadAngle > 0)
                    stats += $" ({supportGem.projectileSpreadAngle}° spread)";
                stats += "</b>\n";
            }

            // Status effect modifiers
            bool hasStatusEffectModifiers = supportGem.igniteEffectivenessMultiplier != 1f || supportGem.igniteDurationMultiplier != 1f ||
                                           supportGem.freezeEffectivenessMultiplier != 1f || supportGem.freezeDurationMultiplier != 1f ||
                                           supportGem.bleedEffectivenessMultiplier != 1f || supportGem.bleedDurationMultiplier != 1f ||
                                           supportGem.shockEffectivenessMultiplier != 1f || supportGem.shockRangeMultiplier != 1f;

            if (hasStatusEffectModifiers)
            {
                stats += "\n<color=#FFD700>Status Effect Modifiers:</color>\n";

                // Ignite modifiers
                if (supportGem.igniteEffectivenessMultiplier != 1f)
                    stats += $"<color=#FF6B35>• Ignite Damage:</color> {(supportGem.igniteEffectivenessMultiplier - 1f) * 100:+0;-0}%\n";
                if (supportGem.igniteDurationMultiplier != 1f)
                    stats += $"<color=#FF6B35>• Ignite Duration:</color> {(supportGem.igniteDurationMultiplier - 1f) * 100:+0;-0}%\n";

                // Freeze modifiers
                if (supportGem.freezeEffectivenessMultiplier != 1f)
                    stats += $"<color=#4FC3F7>• Freeze Effectiveness:</color> {(supportGem.freezeEffectivenessMultiplier - 1f) * 100:+0;-0}%\n";
                if (supportGem.freezeDurationMultiplier != 1f)
                    stats += $"<color=#4FC3F7>• Freeze Duration:</color> {(supportGem.freezeDurationMultiplier - 1f) * 100:+0;-0}%\n";

                // Bleed modifiers
                if (supportGem.bleedEffectivenessMultiplier != 1f)
                    stats += $"<color=#8B0000>• Bleed Damage:</color> {(supportGem.bleedEffectivenessMultiplier - 1f) * 100:+0;-0}%\n";
                if (supportGem.bleedDurationMultiplier != 1f)
                    stats += $"<color=#8B0000>• Bleed Duration:</color> {(supportGem.bleedDurationMultiplier - 1f) * 100:+0;-0}%\n";

                // Shock modifiers
                if (supportGem.shockEffectivenessMultiplier != 1f)
                    stats += $"<color=#FFE082>• Shock Damage:</color> {(supportGem.shockEffectivenessMultiplier - 1f) * 100:+0;-0}%\n";
                if (supportGem.shockRangeMultiplier != 1f)
                    stats += $"<color=#FFE082>• Shock Range:</color> {(supportGem.shockRangeMultiplier - 1f) * 100:+0;-0}%\n";
            }

            // Random modifiers
            if (gem.randomModifiers.Count > 0)
            {
                stats += "\n<size=110%><b><color=#FFD700>Modifiers</color></b></size>\n";
                foreach (var modifier in gem.randomModifiers)
                {
                    stats += $"{modifier.GetDisplayString()}\n";
                }
            }

            return stats.TrimEnd('\n');
        }
        
        return "";
    }
    
    public Sprite GetIcon() => gem.gemDataTemplate?.icon;
    
    public Color GetRarityColor() => gem.RarityColor;
    
    public SelectionType GetSelectionType()
    {
        return gem.IsSkillGem ? SelectionType.SkillGem : SelectionType.SupportGem;
    }
}

/// <summary>
/// Wrapper for PlayerBuffData to implement ISelectable
/// </summary>
public class SelectableBuff : ISelectable
{
    private PlayerBuffData buff;
    
    public SelectableBuff(PlayerBuffData buffData)
    {
        buff = buffData;
    }
    
    public PlayerBuffData GetBuff() => buff;
    
    public string GetDisplayName() => buff.GetDisplayName();
    
    public string GetFormattedDescription() => buff.GetFormattedDescription();
    
    public Sprite GetIcon() => buff.icon;
    
    public Color GetRarityColor() => buff.GetRarityColor();
    
    public SelectionType GetSelectionType() => SelectionType.PlayerBuff;
}