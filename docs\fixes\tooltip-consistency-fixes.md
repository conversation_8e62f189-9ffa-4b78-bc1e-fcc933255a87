# Tooltip Consistency Fixes
**Unity 2D Roguelike Project**

*Fix Date: 2025-07-08*  
*Status: Complete*

## Issues Fixed

### **Issue 1: InventoryManager Missing Status Effect Information** ✅

**Problem**: The inventory tooltips (when hovering over gems) were not showing the new status effect information that was implemented for the gem selection UI.

**Root Cause**: The `GemInstance.GetTooltipText()` method was building tooltips from scratch and not using the enhanced `SkillGemData.GetTooltipText()` method that includes status effect information.

**Solution**: Enhanced `GemInstance.GetTooltipText()` to include status effect information for skill gems and status effect modifiers for support gems.

**Files Modified**:
- `Assets/src/Scripts/Items/GemInstance.cs`

**Changes Made**:
- Added status effect information section for skill gems (lines 478-517)
- Added status effect modifiers section for support gems (lines 612-669)
- Uses backward-compatible default values for existing assets
- Maintains consistent color coding with gem selection UI

### **Issue 2: Gem Selection UI Missing Spell Echo Base Mods** ✅

**Problem**: The gem selection UI was showing "Stats" section for support gems but missing the "Base Mods" section that should display Spell Echo and other special effects.

**Root Cause**: The `SelectableGem.GetFormattedGemStats()` method was only showing basic stats for support gems and missing special effects like Spell Echo.

**Solution**: Added missing special effects display to the gem selection UI, including Spell Echo support.

**Files Modified**:
- `Assets/src/Scripts/UI/GemSelection/ISelectable.cs`

**Changes Made**:
- Added Spell Echo display in `GetFormattedGemStats()` (lines 162-171)
- Maintains consistency with inventory tooltip format
- Shows echo count, delay, and spread radius

## Implementation Details

### **Status Effect Information in Inventory Tooltips**

**For Skill Gems**:
```csharp
// Added to GemInstance.GetTooltipText() after basic stats
if (skillStats.ailmentChance > 0)
{
    sb.Append("\n\n<color=#FFD700>Status Effects (");
    sb.Append(skillStats.ailmentChance.ToString("F0"));
    sb.Append("% chance):</color>");
    
    switch (skillStats.damageType)
    {
        case DamageType.Fire:
            // Shows ignite information with backward compatibility
            break;
        // ... other damage types
    }
}
```

**For Support Gems**:
```csharp
// Added status effect modifiers section
if (hasStatusEffectModifiers)
{
    sb.Append("\n\n<color=#FFD700>Status Effect Modifiers:</color>");
    // Shows percentage modifiers for each status effect type
}
```

### **Spell Echo in Gem Selection UI**

```csharp
// Added to SelectableGem.GetFormattedGemStats()
if (supportGem.addsSpellEcho)
{
    int echoCount = gem.GetSpellEchoCount();
    stats += $"<b>Spell Echo: Recasts {echoCount} time{(echoCount != 1 ? "s" : "")} after {supportGem.echoDelay}s";
    if (supportGem.echoSpreadRadius > 0)
        stats += $" (radius: {supportGem.echoSpreadRadius})";
    stats += "</b>\n";
}
```

## Updated Assets

### **Skill Gems Updated**:
- **Fireball.asset**: Fire damage with 25% ignite chance
- **Lightning Strike.asset**: Lightning damage with 30% shock chance  
- **IceSpear.asset**: Ice damage with 20% freeze chance

### **Support Gems Updated**:
- **ElementalFocus.asset**: Status effect modifiers (+50% ignite, +40% freeze, +60% shock)
- **Spell Echo.asset**: Spell echo with default status effect modifiers

## Testing

### **Tooltip Consistency Test**

Added comprehensive test in `StatusEffectSystemTest.cs`:

```csharp
[Button("Test Tooltip Consistency")]
public void TestTooltipConsistency()
{
    // Compares GemData tooltips (gem selection) vs GemInstance tooltips (inventory)
    // Tests Fireball, Spell Echo, and ElementalFocus
}
```

### **Test Results Expected**:

**Fireball Inventory Tooltip**:
```
Fireball
Common Skill Gem
Tags: Projectile

Launches a fiery projectile that deals fire damage

Stats
Damage: 25
Cooldown: 1.5s
Mana Cost: 10
...
Support Slots: 0

Status Effects (25% chance):
• Ignite: 20% damage over 4.0s
```

**Spell Echo Gem Selection**:
```
Spell Echo
Rare Support Gem

Stats
...

Spell Echo: Recasts 1 time after 0.4s
```

## Verification Steps

### **1. Inventory Tooltips**
1. Open inventory in-game
2. Hover over Fireball gem → Should show status effect information
3. Hover over Spell Echo gem → Should show spell echo details
4. Hover over ElementalFocus gem → Should show status effect modifiers

### **2. Gem Selection UI**
1. Level up to trigger gem selection
2. Select Fireball → Should show ignite information in card
3. Select Spell Echo → Should show "Spell Echo: Recasts 1 time after 0.4s"
4. Select ElementalFocus → Should show status effect modifiers

### **3. Consistency Check**
1. Add `StatusEffectSystemTest` to GameObject
2. Click "Test Tooltip Consistency"
3. Compare console output for both tooltip systems
4. Verify both show same information with appropriate formatting

## Color Coding Consistency

Both tooltip systems now use consistent color coding:

| Element | Color | Usage |
|---------|-------|-------|
| Status Effects Header | #FFD700 (Gold) | Section headers |
| Ignite (Fire) | #FF6B35 (Orange) | Fire status effects |
| Freeze (Ice) | #4FC3F7 (Light Blue) | Ice status effects |
| Bleed (Physical) | #8B0000 (Dark Red) | Physical status effects |
| Shock (Lightning) | #FFE082 (Yellow) | Lightning status effects |

## Backward Compatibility

Both fixes maintain backward compatibility:
- Uses default values when status effect fields are not set
- Existing assets work without modification
- New fields are optional and gracefully handled

## Conclusion

Both tooltip systems now display consistent, comprehensive information:
- ✅ **Inventory tooltips** show status effect information
- ✅ **Gem selection UI** shows Spell Echo and other base mods
- ✅ **Consistent formatting** across both systems
- ✅ **Backward compatibility** with existing assets
- ✅ **Comprehensive testing** suite for validation

Players now receive complete information about gems regardless of where they view them in the UI.
