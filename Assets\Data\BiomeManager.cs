using UnityEngine;
using System.Collections.Generic;
using Sirenix.OdinInspector;

    [System.Serializable]
    public class BiomeManager
    {
        [Title("Biome Configuration")]
        [SerializeField]
        [ListDrawerSettings(ShowIndexLabels = true, ShowFoldout = true)]
        [Tooltip("List of available biomes in order")]
        private List<BiomeData> biomes = new List<BiomeData>();

        [SerializeField, MinValue(1)]
        [Tooltip("How many chunks wide each biome is on the X axis")]
        private int biomeWidthInChunks = 10;

        [SerializeField, MinValue(1)]
        [Tooltip("How many chunks tall each biome is on the Y axis (2D mode only)")]
        [ShowIf("use2DBiomeDistribution")]
        private int biomeHeightInChunks = 5;

        [SerializeField]
        [Tooltip("Random seed for biome generation")]
        private int biomeSeed = 12345;

        [SerializeField]
        [Tooltip("Enable 2D biome distribution (varies on both X and Y axes)")]
        private bool use2DBiomeDistribution = true;
        
        [Serial<PERSON><PERSON><PERSON>, MinValue(1)]
        [Tooltip("Radius in chunks for each biome ring (2D mode)")]
        [ShowIf("use2DBiomeDistribution")]
        private int biomeRingRadiusInChunks = 5;

        [Title("Runtime Info")]
        [ShowInInspector, ReadOnly]
        private Dictionary<ChunkCoordinate, BiomeData> biomeCache2D = new Dictionary<ChunkCoordinate, BiomeData>(100); // Pre-sized
        
        [ShowInInspector, ReadOnly]
        private Dictionary<int, BiomeData> biomeCache = new Dictionary<int, BiomeData>(50); // Pre-sized

        public void Initialize(int seed)
        {
            biomeSeed = seed;
            biomeCache.Clear();
            biomeCache2D.Clear();
        }

        public BiomeData GetBiomeForChunk(int chunkX)
        {
            // For backwards compatibility, use Y=0 when in 2D mode
            if (use2DBiomeDistribution)
            {
                return GetBiomeForChunk(new ChunkCoordinate(chunkX, 0));
            }
            
            if (biomes == null || biomes.Count == 0)
            {
                Debug.LogError("No biomes configured!");
                return null;
            }

            if (biomeCache.TryGetValue(chunkX, out BiomeData cachedBiome))
            {
                return cachedBiome;
            }

            BiomeData biome = DetermineBiome(chunkX);
            biomeCache[chunkX] = biome;
            return biome;
        }

        public BiomeData GetBiomeForChunk(ChunkCoordinate coord)
        {
            if (!use2DBiomeDistribution)
            {
                return GetBiomeForChunk(coord.x);
            }
            
            if (biomes == null || biomes.Count == 0)
            {
                Debug.LogError("No biomes configured!");
                return null;
            }

            if (biomeCache2D.TryGetValue(coord, out BiomeData cachedBiome))
            {
                return cachedBiome;
            }

            BiomeData biome = DetermineBiome2D(coord);
            biomeCache2D[coord] = biome;
            return biome;
        }

        private BiomeData DetermineBiome(int chunkX)
        {
            int biomeIndex = Mathf.FloorToInt((float)chunkX / biomeWidthInChunks);
            
            biomeIndex = Mathf.Abs(biomeIndex) % biomes.Count;
            
            if (chunkX < 0 && biomeIndex != 0)
            {
                biomeIndex = biomes.Count - biomeIndex;
            }

            return biomes[biomeIndex];
        }

        private BiomeData DetermineBiome2D(ChunkCoordinate coord)
        {
            // Calculate distance from center (0,0) in chunks
            float distanceFromCenter = Mathf.Sqrt(coord.x * coord.x + coord.y * coord.y);
            
            // Determine which ring this chunk belongs to
            int ringIndex = Mathf.FloorToInt(distanceFromCenter / biomeRingRadiusInChunks);
            
            // Map ring index to biome index
            int biomeIndex = ringIndex % biomes.Count;
            
            return biomes[biomeIndex];
        }

        public float GetBiomeTransitionWeight(int chunkX)
        {
            float positionInBiome = Mathf.Abs(chunkX % biomeWidthInChunks);
            float distanceFromEdge = Mathf.Min(positionInBiome, biomeWidthInChunks - positionInBiome);
            
            if (distanceFromEdge >= 3) return 0f;
            
            return 1f - (distanceFromEdge / 3f);
        }

        public BiomeData GetAdjacentBiome(int chunkX, bool toRight)
        {
            int adjacentChunkX = chunkX + (toRight ? biomeWidthInChunks : -biomeWidthInChunks);
            return GetBiomeForChunk(adjacentChunkX);
        }

        public bool IsInTransitionZone(int chunkX)
        {
            return GetBiomeTransitionWeight(chunkX) > 0f;
        }

        public bool IsInTileTransitionZone(int worldX, int maxTransitionDistance, int chunkSize = 32)
        {
            int tilesPerBiome = biomeWidthInChunks * chunkSize;
            int positionInBiome = ((worldX % tilesPerBiome) + tilesPerBiome) % tilesPerBiome;
            
            // Check distance from left edge
            if (positionInBiome <= maxTransitionDistance)
                return true;
                
            // Check distance from right edge
            if (positionInBiome >= tilesPerBiome - maxTransitionDistance)
                return true;
                
            return false;
        }

        public BiomeData GetSourceBiomeForTransition(int worldX, int chunkSize = 32)
        {
            int tilesPerBiome = biomeWidthInChunks * chunkSize;
            int positionInBiome = ((worldX % tilesPerBiome) + tilesPerBiome) % tilesPerBiome;
            int currentChunkX = Mathf.FloorToInt((float)worldX / chunkSize);
            
            // Near left edge - source is from the left (previous biome)
            if (positionInBiome < tilesPerBiome / 2)
            {
                int sourceChunkX = currentChunkX - biomeWidthInChunks;
                return GetBiomeForChunk(sourceChunkX);
            }
            // Near right edge - source is from the right (next biome)
            else
            {
                int sourceChunkX = currentChunkX + biomeWidthInChunks;
                return GetBiomeForChunk(sourceChunkX);
            }
        }

        public bool IsTransitionFromLeft(int worldX, int chunkSize = 32)
        {
            int tilesPerBiome = biomeWidthInChunks * chunkSize;
            int positionInBiome = ((worldX % tilesPerBiome) + tilesPerBiome) % tilesPerBiome;
            
            // If we're near the left edge, transition comes from left
            // If we're near the right edge, transition comes from right
            return positionInBiome < tilesPerBiome / 2;
        }

        public float GetTransitionStrength(int worldX, int maxTransitionDistance, int chunkSize = 32)
        {
            int tilesPerBiome = biomeWidthInChunks * chunkSize;
            int positionInBiome = ((worldX % tilesPerBiome) + tilesPerBiome) % tilesPerBiome;
            
            int distanceFromEdge = Mathf.Min(positionInBiome, tilesPerBiome - positionInBiome);
            
            if (distanceFromEdge > maxTransitionDistance) return 0f;
            
            return 1f - ((float)distanceFromEdge / maxTransitionDistance);
        }

        public bool IsExactlyAtBiomeBoundary(int worldX, int chunkSize = 32)
        {
            int tilesPerBiome = biomeWidthInChunks * chunkSize;
            int positionInBiome = ((worldX % tilesPerBiome) + tilesPerBiome) % tilesPerBiome;
            
            // Only place transition at left edge (0) to avoid double transitions
            return positionInBiome == 0;
        }

        public BiomeData GetSourceBiomeForBoundary(int worldX, int chunkSize = 32)
        {
            int tilesPerBiome = biomeWidthInChunks * chunkSize;
            int positionInBiome = ((worldX % tilesPerBiome) + tilesPerBiome) % tilesPerBiome;
            int currentChunkX = Mathf.FloorToInt((float)worldX / chunkSize);
            
            // Only at left edge (0) - source is from the left (previous biome)
            if (positionInBiome == 0)
            {
                int sourceChunkX = currentChunkX - 1;
                return GetBiomeForChunk(sourceChunkX);
            }
            
            return null;
        }

        public bool IsBoundaryTransitionFromLeft(int worldX, int chunkSize = 32)
        {
            int tilesPerBiome = biomeWidthInChunks * chunkSize;
            int positionInBiome = ((worldX % tilesPerBiome) + tilesPerBiome) % tilesPerBiome;
            
            // At left edge (0) - we need rightTransitionTiles (pointing right →)
            // Return false means use rightTransitionTiles
            return false;
        }

        #if UNITY_EDITOR
        [Button("Log Biome Distribution", ButtonSizes.Medium)]
        private void LogBiomeDistribution()
        {
            if (biomes == null || biomes.Count == 0)
            {
                Debug.LogError("No biomes configured!");
                return;
            }

            if (use2DBiomeDistribution)
            {
                // Log 2D ring distribution
                string distribution = "Biome Ring Distribution (2D Mode):\n";
                distribution += $"Ring Radius: {biomeRingRadiusInChunks} chunks\n\n";
                
                for (int ring = 0; ring < 5; ring++)
                {
                    float distance = ring * biomeRingRadiusInChunks;
                    int biomeIndex = ring % biomes.Count;
                    distribution += $"Ring {ring} (distance {distance:F1}-{distance + biomeRingRadiusInChunks:F1} chunks): {biomes[biomeIndex].BiomeName}\n";
                }
                
                distribution += "\nSample positions:\n";
                Vector2Int[] samplePositions = {
                    new Vector2Int(0, 0), new Vector2Int(3, 0), new Vector2Int(5, 0),
                    new Vector2Int(8, 0), new Vector2Int(10, 10), new Vector2Int(-15, 0)
                };
                
                foreach (var pos in samplePositions)
                {
                    ChunkCoordinate coord = new ChunkCoordinate(pos.x, pos.y);
                    BiomeData biome = GetBiomeForChunk(coord);
                    float distance = Mathf.Sqrt(pos.x * pos.x + pos.y * pos.y);
                    distribution += $"  Chunk ({pos.x},{pos.y}) - distance {distance:F1}: {biome.BiomeName}\n";
                }
                
                Debug.Log(distribution);
            }
            else
            {
                // Original 1D distribution log
                string distribution = "Biome Distribution (1D Mode):\n";
                for (int x = -20; x <= 20; x++)
                {
                    BiomeData biome = GetBiomeForChunk(x);
                    bool inTransition = IsInTransitionZone(x);
                    distribution += $"Chunk X={x}: {biome.BiomeName}";
                    if (inTransition)
                    {
                        distribution += " (Transition Zone)";
                    }
                    distribution += "\n";
                }
                Debug.Log(distribution);
            }
        }

        [Button("Validate Biomes", ButtonSizes.Medium)]
        private void ValidateBiomes()
        {
            if (biomes == null || biomes.Count == 0)
            {
                Debug.LogError("No biomes configured!");
                return;
            }

            bool hasErrors = false;
            for (int i = 0; i < biomes.Count; i++)
            {
                if (biomes[i] == null)
                {
                    Debug.LogError($"Biome at index {i} is null!");
                    hasErrors = true;
                }
                else if (biomes[i].PrimaryGroundTile == null)
                {
                    Debug.LogError($"Biome '{biomes[i].BiomeName}' has no primary ground tile!");
                    hasErrors = true;
                }
            }

            if (!hasErrors)
            {
                Debug.Log($"<color=green>All {biomes.Count} biomes validated successfully!</color>");
            }
        }
        #endif

        // Cache for GetAllBiomes to avoid allocations
        private List<BiomeData> allBiomesCache = null;
        
        public List<BiomeData> GetAllBiomes()
        {
            // Only create new list if biomes have changed
            if (allBiomesCache == null || allBiomesCache.Count != biomes.Count)
            {
                allBiomesCache = new List<BiomeData>(biomes);
            }
            return allBiomesCache;
        }

        public int GetBiomeWidthInChunks()
        {
            return biomeWidthInChunks;
        }

        public void AddBiome(BiomeData biome)
        {
            if (biome != null && !biomes.Contains(biome))
            {
                biomes.Add(biome);
                biomeCache.Clear();
                allBiomesCache = null; // Invalidate cache
            }
        }

        public void RemoveBiome(BiomeData biome)
        {
            if (biomes.Remove(biome))
            {
                biomeCache.Clear();
                allBiomesCache = null; // Invalidate cache
            }
        }

        public void ClearCache()
        {
            biomeCache.Clear();
            biomeCache2D.Clear();
        }
        
        // Get biome for a specific world position (tile-based)
        public BiomeData GetBiomeForWorldPosition(Vector3Int worldPos, int chunkSize = 32)
        {
            if (use2DBiomeDistribution)
            {
                return GetBiomeForWorldPosition2D(worldPos, chunkSize);
            }
            else
            {
                return GetBiomeForWorldPosition1D(worldPos.x, chunkSize);
            }
        }
        
        private BiomeData GetBiomeForWorldPosition1D(int worldX, int chunkSize = 32)
        {
            if (biomes == null || biomes.Count == 0)
            {
                Debug.LogError("No biomes configured!");
                return null;
            }
            
            // Calculate biome based on world position, not chunk
            int tilesPerBiome = biomeWidthInChunks * chunkSize;
            int biomeIndex = Mathf.FloorToInt((float)worldX / tilesPerBiome);
            
            biomeIndex = Mathf.Abs(biomeIndex) % biomes.Count;
            
            if (worldX < 0 && biomeIndex != 0)
            {
                biomeIndex = biomes.Count - biomeIndex;
            }
            
            return biomes[biomeIndex];
        }
        
        private BiomeData GetBiomeForWorldPosition2D(Vector3Int worldPos, int chunkSize = 32)
        {
            if (biomes == null || biomes.Count == 0)
            {
                Debug.LogError("No biomes configured!");
                return null;
            }
            
            // Calculate distance from center in world units (tiles)
            float distanceFromCenter = Mathf.Sqrt(worldPos.x * worldPos.x + worldPos.y * worldPos.y);
            float ringRadiusInTiles = biomeRingRadiusInChunks * chunkSize;
            
            // Determine which ring this position belongs to
            int ringIndex = Mathf.FloorToInt(distanceFromCenter / ringRadiusInTiles);
            
            // Map ring index to biome index
            int biomeIndex = ringIndex % biomes.Count;
            
            return biomes[biomeIndex];
        }

        // 2D Biome boundary detection methods
        public bool IsAtBiomeBoundary2D(Vector3Int worldPos, int chunkSize = 32)
        {
            if (!use2DBiomeDistribution) return false;
            
            BiomeData currentBiome = GetBiomeForWorldPosition(worldPos, chunkSize);
            
            // Check all 8 directions using cached Vector3Int
            for (int dx = -1; dx <= 1; dx++)
            {
                for (int dy = -1; dy <= 1; dy++)
                {
                    if (dx == 0 && dy == 0) continue;
                    
                    // Use cached Vector3Int to avoid allocation
                    cachedCalcPos.x = worldPos.x + dx;
                    cachedCalcPos.y = worldPos.y + dy;
                    cachedCalcPos.z = worldPos.z;
                    BiomeData neighborBiome = GetBiomeForWorldPosition(cachedCalcPos, chunkSize);
                    
                    if (currentBiome != neighborBiome)
                    {
                        return true;
                    }
                }
            }
            
            return false;
        }

        // Reusable collections for GetNeighboringBiomes2D to avoid allocations
        private readonly HashSet<BiomeData> neighboringBiomesCache = new HashSet<BiomeData>();
        private readonly List<BiomeData> neighboringBiomesList = new List<BiomeData>(8);
        private Vector3Int cachedNeighborPos = new Vector3Int();
        
        // Static arrays for GetTransitionDirections2D to avoid allocations
        private static readonly Vector3Int[] cardinalOffsets = {
            new Vector3Int(0, 1, 0),   // North
            new Vector3Int(0, -1, 0),  // South
            new Vector3Int(1, 0, 0),   // East
            new Vector3Int(-1, 0, 0)   // West
        };
        
        private static readonly TransitionDirection[] cardinalDirs = {
            TransitionDirection.North,
            TransitionDirection.South,
            TransitionDirection.East,
            TransitionDirection.West
        };
        
        // Cached Vector3Int for calculations to avoid allocations
        private Vector3Int cachedCalcPos = new Vector3Int();
        
        public List<BiomeData> GetNeighboringBiomes2D(Vector3Int worldPos, int chunkSize = 32)
        {
            neighboringBiomesList.Clear();
            
            if (!use2DBiomeDistribution) return neighboringBiomesList;
            
            neighboringBiomesCache.Clear();
            BiomeData currentBiome = GetBiomeForWorldPosition(worldPos, chunkSize);
            
            // Check all 8 directions using cached position
            for (int dx = -1; dx <= 1; dx++)
            {
                for (int dy = -1; dy <= 1; dy++)
                {
                    if (dx == 0 && dy == 0) continue;
                    
                    cachedNeighborPos.x = worldPos.x + dx;
                    cachedNeighborPos.y = worldPos.y + dy;
                    cachedNeighborPos.z = worldPos.z;
                    BiomeData neighborBiome = GetBiomeForWorldPosition(cachedNeighborPos, chunkSize);
                    
                    if (neighborBiome != currentBiome && neighborBiome != null)
                    {
                        neighboringBiomesCache.Add(neighborBiome);
                    }
                }
            }
            
            // Copy to list without allocation
            foreach (var biome in neighboringBiomesCache)
            {
                neighboringBiomesList.Add(biome);
            }
            
            return neighboringBiomesList;
        }

        public TransitionDirection GetTransitionDirections2D(Vector3Int worldPos, int chunkSize = 32)
        {
            if (!use2DBiomeDistribution) return TransitionDirection.None;
            
            TransitionDirection directions = TransitionDirection.None;
            BiomeData currentBiome = GetBiomeForWorldPosition(worldPos, chunkSize);
            
            // Use static arrays - no allocations
            for (int i = 0; i < cardinalOffsets.Length; i++)
            {
                // Use cached Vector3Int to avoid allocation
                cachedCalcPos.x = worldPos.x + cardinalOffsets[i].x;
                cachedCalcPos.y = worldPos.y + cardinalOffsets[i].y;
                cachedCalcPos.z = worldPos.z + cardinalOffsets[i].z;
                BiomeData neighborBiome = GetBiomeForWorldPosition(cachedCalcPos, chunkSize);
                
                if (neighborBiome != currentBiome)
                {
                    directions |= cardinalDirs[i];
                }
            }
            
            // Note: We don't check diagonal directions separately anymore.
            // Corner tiles should only be placed when both cardinal directions are set.
            // The TransitionTileGenerator will handle the corner logic based on cardinal directions.
            
            return directions;
        }

        public bool IsUse2DBiomeDistribution()
        {
            return use2DBiomeDistribution;
        }
    }
