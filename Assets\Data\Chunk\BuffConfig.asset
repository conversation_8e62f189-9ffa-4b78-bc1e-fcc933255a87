%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01a36f52053dbf14ba57f45b0db4ee33, type: 3}
  m_Name: BuffConfig
  m_EditorClassIdentifier: Assembly-CSharp::ChunkBuffConfig
  configName: Default Chunk Buff Config
  description: Configuration for chunk-based enemy attack buffs and scaling
  baseDistanceThreshold: 1
  damagePerDistance: 5
  critChancePerDistance: 2
  attackSpeedPerDistance: 1
  maxScalingDistance: 20
  randomBuffChance: 1
  possibleRandomBuffs:
  - name: Sharp Weapons
    weight: 0.3
    modifiers:
    - type: 1
      value: 15
      source: random_sharp
    - type: 2
      value: 10
      source: random_sharp
    description: Weapons deal extra damage and have increased crit chance
  - name: Multi-Shot
    weight: 0.2
    modifiers:
    - type: 4
      value: 2
      source: random_multishot
    description: Ranged units fire additional projectiles
  - name: Berserker Rage
    weight: 0.25
    modifiers:
    - type: 6
      value: 250
      source: random_berserker
    - type: 0
      value: 5
      source: random_berserker
    description: Faster attacks and movement
  - name: Precision Strike
    weight: 0.15
    modifiers:
    - type: 2
      value: 25
      source: random_precision
    - type: 3
      value: 50
      source: random_precision
    description: Massive crit chance and damage bonus
  - name: Long Range
    weight: 0.1
    modifiers:
    - type: 7
      value: 30
      source: random_range
    - type: 5
      value: 50
      source: random_range
    description: Increased attack range and projectile speed
  eliteZoneInterval: 10
  eliteZoneDamageBonus: 25
  eliteZoneCritBonus: 15
  eliteZoneProjectileBonus: 2
  eliteZoneAttackSpeedBonus: 20
