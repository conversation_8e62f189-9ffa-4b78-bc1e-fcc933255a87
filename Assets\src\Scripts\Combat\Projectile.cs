using UnityEngine;
using Sirenix.OdinInspector;

[RequireComponent(typeof(SpatialCollider))]
public class Projectile : MonoBehaviour, ISpawnable, ISpatialCollisionHandler
{
    [Title("Projectile Settings")]
    [SerializeField] private float defaultSpeed = 10f;
    [SerializeField] private float defaultLifetime = 2f; // Changed from range to lifetime
    [SerializeField] private float baseDamage = 10f;
    
    [Title("Particle Effects")]
    [SerializeField] private bool useImpactParticles = true;
    [ShowIf("useImpactParticles")]
    [SerializeField] private ParticleType impactParticleType = ParticleType.SparkImpact;
    [ShowIf("useImpactParticles")]
    [SerializeField] private int impactParticleCount = 10;
    
    [SerializeField] private bool useTrailParticles = false;
    [ShowIf("useTrailParticles")]
    [SerializeField] private ParticleType trailParticleType = ParticleType.FireTrail;
    [ShowIf("useTrailParticles")]
    [Serial<PERSON>Field] private float trailInterval = 0.1f;
    
    [SerializeField] private bool useDespawnParticles = true;
    [ShowIf("useDespawnParticles")]
    [SerializeField] private ParticleType despawnParticleType = ParticleType.SmokeImpact;
    [ShowIf("useDespawnParticles")]
    [SerializeField] private int despawnParticleCount = 5;
    
    [InfoBox("Optional: Assign a child transform for precise particle spawn position")]
    [SerializeField] private Transform particleSpawnPoint;
    
    // Runtime values
    public float speed { get; set; }
    public float lifetime { get; set; } // Changed from maxRange to lifetime
    public float damage { get; set; }
    public float critChance { get; set; }
    public float critMultiplier { get; set; }
    public DamageType damageType { get; set; } = DamageType.Physical;
    public float ailmentChance { get; set; } = 0f;

    // Gem data for status effect configuration
    public SkillGemData skillGemData { get; set; }
    public System.Collections.Generic.List<GemInstance> supportGems { get; set; }
    
    // Support gem effects
    private bool isPiercing;
    private int pierceCount;
    private int currentPierces;
    
    private bool isChaining;
    private int chainCount;
    private int currentChains;
    private GameObject lastTarget;
    
    private bool isFork;
    private int forkCount;
    private float forkAngle;
    private bool hasForked;
    
    private bool hasAreaDamage;
    private float areaRadius;
     
    private Vector2 _direction;
    private float _timeAlive; // Changed from _distanceTraveled
    private SpatialCollider _spatialCollider;
    private bool _isActive;
    private CollisionLayers _currentLayer;
    
    private void Awake()
    {
        _spatialCollider = GetComponent<SpatialCollider>();
    }
    
    public void Initialize(Vector2 position, Vector2 direction, float damageMultiplier = 1f, CollisionLayers layer = CollisionLayers.PlayerProjectile)
    {
        Initialize(position, direction, damageMultiplier, layer, defaultSpeed, defaultLifetime);
    }
    
    public void Initialize(Vector2 position, Vector2 direction, float damageMultiplier, CollisionLayers layer, float projectileSpeed, float projectileLifetime)
    {
        transform.position = position;
        _direction = direction.normalized;
        _timeAlive = 0f;
        this.damage = baseDamage * damageMultiplier;
        this.speed = projectileSpeed;
        this.lifetime = projectileLifetime;
        _isActive = true;
        
        _spatialCollider.SetLayer(layer);
        _currentLayer = layer;
        
        // Rotate to face direction
        float angle = Mathf.Atan2(_direction.y, _direction.x) * Mathf.Rad2Deg;
        transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
        
        // Start trail particles if enabled
        if (useTrailParticles && ParticleEffectManager.Instance != null)
        {
        ParticleEffectManager.Instance.StartContinuousEffect(trailParticleType, transform, trailInterval);
        }
    }
    
    private void Update()
    {
        if (!_isActive) return;
        
        // Move projectile
        Vector2 movement = _direction * speed * Time.deltaTime;
        transform.position += (Vector3)movement;
        _timeAlive += Time.deltaTime;
        
        // Check if exceeded lifetime
        if (_timeAlive >= lifetime)
        {
        SpawnDespawnParticles();
        Deactivate();
        }
    }
    
    // ISpatialCollisionHandler implementation
    public void HandleCollisionEnter(CollisionInfo collision)
    {
        // Skip if we're chaining and this is our last target
        if (isChaining && collision.Other.GameObject == lastTarget)
        return;
        
        // We can damage any layer that has a HealthComponent, so we don't need to check specific layers here.
        // The collision matrix in SpatialCollider should prevent projectiles from hitting their own side (e.g. player hitting player).
        if (ApplyDamage(collision.Other.GameObject))
        {
        // Spawn impact particles
        SpawnImpactParticles(collision.ContactPoint);
        
        // Handle area damage
        if (hasAreaDamage)
        {
            ApplyAreaDamage(transform.position);
        }
        
        // Handle forking (fork takes priority over chain)
        if (isFork && !hasForked)
        {
            ForkProjectiles(collision);
            hasForked = true;
            // Deactivate the original projectile after forking
            Deactivate();
            return;
        }
        
        // Handle chaining
        if (isChaining && currentChains < chainCount)
        {
            ChainToNextTarget(collision.Other.GameObject);
            currentChains++;
            return;
        }
        
        // Handle piercing
        if (isPiercing && currentPierces < pierceCount)
        {
            currentPierces++;
            return; // Continue through target
        }
        
        // Deactivate only if we successfully hit a damageable object.
        Deactivate();
        return;
        }

        // If the object wasn't damageable, check if it's a wall/environment to despawn.
        if (collision.Other.Layer.HasFlag(CollisionLayers.Wall) || 
        collision.Other.Layer.HasFlag(CollisionLayers.Environment))
        {
        // Spawn impact particles for wall hits too
        SpawnImpactParticles(collision.ContactPoint);
        Deactivate();
        }
    }
    
    public void HandleCollisionStay(CollisionInfo collision) { }
    public void HandleCollisionExit(CollisionInfo collision) { }
    public void HandleTriggerEnter(CollisionInfo collision) { }
    public void HandleTriggerStay(CollisionInfo collision) { }
    public void HandleTriggerExit(CollisionInfo collision) { }
    
    private bool ApplyDamage(GameObject target)
    {
        // Calculate final damage with crit
        float finalDamage = damage;
        bool isCrit = Random.Range(0f, 100f) < critChance;
        if (isCrit)
        {
            finalDamage *= critMultiplier;
        }
        
        // Create damage info with gem data for status effect configuration
        DamageInfo damageInfo = new DamageInfo(
            Mathf.RoundToInt(finalDamage),
            damageType,
            isCrit,
            critMultiplier,
            "Projectile",
            ailmentChance,
            skillGemData,
            supportGems
        );
        
        // First, handle player damage via PlayerManager to avoid unnecessary pool lookups
        if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
        {
        PlayerManager.DealDamageToPlayer(damageInfo);
        return true;
        }

        // Fall back to pooled or direct HealthComponent lookup for all other targets
        if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
        {
        healthComponent.TakeDamage(damageInfo);
        return true;
        }
        else if (target.TryGetComponent<HealthComponent>(out var directHealth))
        {
        directHealth.TakeDamage(damageInfo);
        return true;
        }

        return false;
    }
    
    private void ApplyAreaDamage(Vector3 center)
    {
        // Find all enemies in radius using custom collision system
        if (CollisionManager.Instance == null) return;
        
        var collidables = CollisionManager.Instance.GetCollidersInRadius(
            center, areaRadius, CollisionLayers.Enemy);
            
        foreach (var collidable in collidables)
        {
            var target = collidable.GameObject;
            if (target == gameObject) continue; // Skip self
            
            // Apply reduced damage for area effect (70% of base)
            float areaDamage = damage * 0.7f;
            bool isCrit = Random.Range(0f, 100f) < critChance;
            if (isCrit)
            {
                areaDamage *= critMultiplier;
            }
            
            // Create damage info for area damage with gem data
            DamageInfo areaDamageInfo = new DamageInfo(
                Mathf.RoundToInt(areaDamage),
                damageType,
                isCrit,
                critMultiplier,
                "Projectile_Area",
                ailmentChance,
                skillGemData,
                supportGems
            );
            
            // Handle player damage via PlayerManager
            if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
            {
                PlayerManager.DealDamageToPlayer(areaDamageInfo);
            }
            // Handle enemy damage via cached components
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
            {
                healthComponent.TakeDamage(areaDamageInfo);
            }
            else if (target.TryGetComponent<HealthComponent>(out var directHealth))
            {
                directHealth.TakeDamage(areaDamageInfo);
            }
        }
    }
    
    private void ChainToNextTarget(GameObject currentTarget)
    {
        lastTarget = currentTarget;
        
        // Find nearest enemy within chain range using custom collision system
        float searchRadius = 5f;
        if (CollisionManager.Instance == null)
        {
            Deactivate();
            return;
        }
        
        var collidables = CollisionManager.Instance.GetCollidersInRadius(
            transform.position, searchRadius, CollisionLayers.Enemy);
        
        GameObject nearestTarget = null;
        float nearestDistance = float.MaxValue;
        
        foreach (var collidable in collidables)
        {
            var target = collidable.GameObject;
            if (target == currentTarget) continue; // Skip current target
            if (target == gameObject) continue; // Skip self
            
            // Check if target has health (is damageable)
            bool hasHealth = false;
            if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
            {
                hasHealth = true;
            }
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var _))
            {
                hasHealth = true;
            }
            else if (target.TryGetComponent<HealthComponent>(out var _))
            {
                hasHealth = true;
            }
            
            if (hasHealth)
            {
                float distance = Vector2.Distance(transform.position, target.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestTarget = target;
                }
            }
        }
        
        if (nearestTarget != null)
        {
            // Redirect projectile to new target
            Vector2 newDirection = (nearestTarget.transform.position - transform.position).normalized;
            _direction = newDirection;
            
            // Reset lifetime for chain
            _timeAlive = 0f;
            
            // Reduce damage for each chain (80% of previous)
            damage *= 0.8f;
            
            // Update rotation
            float angle = Mathf.Atan2(_direction.y, _direction.x) * Mathf.Rad2Deg;
            transform.rotation = Quaternion.AngleAxis(angle, Vector3.forward);
        }
        else
        {
            // No valid targets, deactivate
            Deactivate();
        }
    }
    
    private void ForkProjectiles(CollisionInfo collision)
    {
        if (PoolManager.Instance == null || collision.Other == null) return;
        
        Vector2 impactPoint = collision.ContactPoint;
        Vector2 collisionNormal = collision.Normal;
        
        // Calculate offset based on the hit object's size
        float spawnOffset = 0.2f; // Base offset
        
        if (collision.Other.Shape == ColliderShape.Circle)
        {
            // For circles, use radius
            spawnOffset += collision.Other.Radius;
        }
        else if (collision.Other.Shape == ColliderShape.Box)
        {
            // For boxes, use the larger dimension
            spawnOffset += Mathf.Max(collision.Other.Size.x, collision.Other.Size.y) * 0.5f;
        }
        
        // Add a small safety margin
        spawnOffset += 0.3f;
        
        // Spawn point: offset from impact point along the collision normal
        // The normal points away from the collider we hit
        Vector2 spawnOrigin = impactPoint + (collisionNormal * spawnOffset);
        
        // Debug.Log($"Fork: impact={impactPoint}, normal={collisionNormal}, offset={spawnOffset}, spawn={spawnOrigin}");
        
        // Calculate the base angle spread
        float anglePerFork = forkCount > 1 ? forkAngle / (forkCount - 1) : 0f;
        float startAngle = -forkAngle / 2f;
        
        // Get the current projectile's direction angle
        float baseAngle = Mathf.Atan2(_direction.y, _direction.x) * Mathf.Rad2Deg;
        
        // Spawn forked projectiles
        for (int i = 0; i < forkCount; i++)
        {
            float currentAngle = baseAngle + startAngle + (anglePerFork * i);
            float radians = currentAngle * Mathf.Deg2Rad;
            Vector2 forkDirection = new Vector2(Mathf.Cos(radians), Mathf.Sin(radians));
            
            // Spawn a new projectile
            GameObject forkedProjectile = PoolManager.Instance.Spawn(gameObject, spawnOrigin, Quaternion.Euler(0, 0, currentAngle));
            if (forkedProjectile != null && forkedProjectile.TryGetComponent<Projectile>(out var projectileComponent))
            {
                // Initialize the forked projectile with reduced damage
                float forkedDamage = damage * 0.7f; // Fork damage is 70% of original
                projectileComponent.Initialize(spawnOrigin, forkDirection, forkedDamage / baseDamage, _currentLayer, speed, lifetime);
                
                // Copy critical stats
                projectileComponent.critChance = critChance;
                projectileComponent.critMultiplier = critMultiplier;
                
                // Forked projectiles can pierce but not fork again
                if (isPiercing)
                {
                    projectileComponent.SetPiercing(true, pierceCount);
                }
                
                // Disable forking on the forked projectiles to prevent infinite forks
                projectileComponent.SetFork(false);
            }
        }
    }
    
    private void SpawnImpactParticles(Vector2 impactPoint)
    {
        if (!useImpactParticles || ParticleEffectManager.Instance == null) return;
        
        // Use particle spawn point if available, otherwise use impact point
        Vector3 spawnPosition = particleSpawnPoint != null ? particleSpawnPoint.position : (Vector3)impactPoint;
        
        ParticleEffectManager.Instance.SpawnParticle(impactParticleType, spawnPosition, impactParticleCount);
    }
    
    private void SpawnDespawnParticles()
    {
        if (!useDespawnParticles || ParticleEffectManager.Instance == null) return;
        
        // Use particle spawn point if available, otherwise use current position
        Vector3 spawnPosition = particleSpawnPoint != null ? particleSpawnPoint.position : transform.position;
        
        ParticleEffectManager.Instance.SpawnParticle(despawnParticleType, spawnPosition, despawnParticleCount);
    }
    
    private void Deactivate()
    {
        _isActive = false;
        
        // Stop trail particles if active
        if (useTrailParticles && ParticleEffectManager.Instance != null)
        {
        ParticleEffectManager.Instance.StopContinuousEffect(transform);
        }
        
        if (PoolManager.Instance != null)
        {
        PoolManager.Instance.Despawn(gameObject);
        }
        else
        {
        gameObject.SetActive(false);
        }
    }
    
    // ISpawnable implementation
    public void OnSpawn()
    {
        _isActive = true;
       
    }
    
    public void OnDespawn()
    {
        _isActive = false;
        damage = baseDamage; // Reset to base damage
        speed = defaultSpeed; // Reset speed
        lifetime = defaultLifetime; // Reset lifetime
        damageType = DamageType.Physical; // Reset damage type
        
        // Reset support gem effects
        isPiercing = false;
        pierceCount = 0;
        currentPierces = 0;
        
        isChaining = false;
        chainCount = 0;
        currentChains = 0;
        lastTarget = null;
        
        isFork = false;
        forkCount = 0;
        forkAngle = 30f;
        hasForked = false;
        
        hasAreaDamage = false;
        areaRadius = 0f;
        
        // Ensure particles are stopped
        if (ParticleEffectManager.Instance != null)
        {
        ParticleEffectManager.Instance.StopContinuousEffect(transform);
        }
    }
    
    // Support gem effect setters
    public void SetPiercing(bool enable, int count = 999)
    {
        isPiercing = enable;
        pierceCount = count;
        currentPierces = 0;
    }
    
    public void SetChaining(bool enable, int count = 3)
    {
        isChaining = enable;
        chainCount = count;
        currentChains = 0;
    }
    
    public void SetFork(bool enable, int count = 2, float angle = 30f)
    {
        isFork = enable;
        forkCount = count;
        forkAngle = angle;
        hasForked = false;
    }
    
    public void SetAreaDamage(bool enable, float radius = 2f)
    {
        hasAreaDamage = enable;
        areaRadius = radius;
    }
}