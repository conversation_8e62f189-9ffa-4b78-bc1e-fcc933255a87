%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d3d0b52bf8c3e684f96ec4333fba0f67, type: 3}
  m_Name: VolleySupport
  m_EditorClassIdentifier: 
  icon: {fileID: 21300000, guid: b95b196523125424e9b805890846cccb, type: 3}
  backgroundIcon: {fileID: 0}
  foregroundIcon: {fileID: 0}
  gemName: Volley Support
  description: Fires projectiles in parallel lines that maintain their formation
    over distance. Less damage but more consistent hits
  rarity: 0
  useIntegerScaling: 1
  commonStatMultiplier: 0.7
  uncommonStatMultiplier: 0.85
  rareStatMultiplier: 1
  epicStatMultiplier: 1.15
  uniqueStatMultiplier: 1.3
  commonIntMultiplier: 1
  uncommonIntMultiplier: 2
  rareIntMultiplier: 2
  epicIntMultiplier: 3
  uniqueIntMultiplier: 3
  compatibleTags: 2
  damageIncreased: -15
  damageMore: 1
  cooldownMultiplier: 1
  manaCostMultiplier: 1.2
  attackSpeedMultiplier: 1
  addedCritChance: 0
  critMultiplierModifier: 1
  addsPierce: 0
  addsChain: 0
  addsAreaDamage: 0
  addsMultipleProjectiles: 1
  extraProjectiles: 2
  projectileSpreadAngle: 5
  useParallelProjectiles: 1
  projectileLateralOffset: 0.8
  chainCount: 2
  areaRadius: 3
  addsFork: 0
  forkCount: 2
  forkAngle: 30
  addsSpellEcho: 0
  echoDelay: 0.4
  echoCount: 1
  echoSpreadRadius: 0
