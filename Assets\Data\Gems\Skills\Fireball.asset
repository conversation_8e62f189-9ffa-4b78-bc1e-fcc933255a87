%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc7afdaae02bac047acf72aabe6a2a9c, type: 3}
  m_Name: Fireball
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Items.SkillGemData
  icon: {fileID: 0}
  gemName: Fireball
  description: Launches a fiery projectile that deals fire damage
  rarity: 0
  level: 1
  maxLevel: 20
  skillType: 1
  skillPrefab: {fileID: 0}
  baseDamage: 25
  cooldown: 1.5
  manaCost: 10
  projectileSpeed: 10
  duration: 2
  targetGroundPosition: 1
  gemTags: 2
  attackSpeedMultiplier: 1
  critChance: 5
  critMultiplier: 1.5
  damageType: 1
  ailmentChance: 25
  ignitePercent: 0.2
  igniteDuration: 4
  freezeSlowAmount: 0.5
  freezeDuration: 2
  bleedPercent: 0.15
  bleedDuration: 6
  shockChainDamage: 0.1
  shockChainRange: 3
  shockDuration: 2
