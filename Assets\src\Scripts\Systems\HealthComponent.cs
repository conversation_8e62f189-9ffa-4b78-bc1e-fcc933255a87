using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Events;

public class HealthComponent : MonoB<PERSON><PERSON><PERSON>, IDamageable
{
    [SerializeField, MinValue(1), Tooltip("The maximum health of the object.")]
    private float _maxHealth = 100f;

    [ShowInInspect<PERSON>, ReadOnly, ProgressBar(0, "_maxHealth", ColorGetter = "GetHealthBarColor")]
    private float _currentHealth;

    public float CurrentHealth => _currentHealth;
    public float MaxHealth => _maxHealth;
    
    // Status effect manager
    private StatusEffectManager statusEffectManager;

    [FoldoutGroup("Events")]
    public UnityEvent OnDeath { get; } = new UnityEvent();
    
    [FoldoutGroup("Events")]
    public UnityEvent<float> OnDamage { get; } = new UnityEvent<float>();
    
    [FoldoutGroup("Splatter")]
    [SerializeField] protected bool m_useHitSplatter = false;
    [Tooltip("Spawn blood splatter when taking damage")]
    
    [FoldoutGroup("Splatter")]
    [SerializeField] protected bool m_useDeathSplatter = false;
    [Tooltip("Spawn blood splatter when dying")]
    
    protected bool useHitSplatter => m_useHitSplatter;
    protected bool useDeathSplatter => m_useDeathSplatter;

    protected virtual void Awake()
    {
        _currentHealth = _maxHealth;
        
        // Get or add StatusEffectManager
        statusEffectManager = GetComponent<StatusEffectManager>();
        if (statusEffectManager == null)
        {
            statusEffectManager = gameObject.AddComponent<StatusEffectManager>();
        }
    }

    
    public virtual void TakeDamage(float damageAmount)
    {
        // Convert to DamageInfo and call the new method
        TakeDamage(new DamageInfo(damageAmount, DamageType.Physical));
    }
    
    public virtual void TakeDamage(DamageInfo damageInfo)
    {
        if (damageInfo.amount <= 0 || _currentHealth <= 0)
        {
            return;
        }

        _currentHealth -= damageInfo.amount;
        OnDamage?.Invoke(damageInfo.amount);
        
        // Log damage type for debugging with 2 decimal places
        Debug.Log($"[{gameObject.name}] took {damageInfo.amount:F2} {damageInfo.type} damage{(damageInfo.isCritical ? " [CRIT]" : "")}");
        
        // Apply status effects based on damage type using centralized helper
        StatusEffectHelper.TryApplyAilment(damageInfo, damageInfo.amount, statusEffectManager);
        
        // Spawn hit splatter
        if (useHitSplatter)
        {
            var splatterManager = FindObjectOfType<SplatterSystem.BitmapSplatterManager>();
            if (splatterManager != null)
            {
                splatterManager.Spawn(transform.position);
            }
        }

        if (_currentHealth <= 0)
        {
            _currentHealth = 0;
            
            // Spawn death splatter
            if (useDeathSplatter)
            {
                var splatterManager = FindObjectOfType<SplatterSystem.BitmapSplatterManager>();
                if (splatterManager != null)
                {
                    splatterManager.Spawn(transform.position);
                }
            }
            
            OnDeath?.Invoke();
        }
    }

    [Button("Heal")]
    public void Heal(float healAmount)
    {
        if (healAmount <= 0) return;
        _currentHealth = Mathf.Min(_currentHealth + healAmount, _maxHealth);
    }

    public void SetMaxHealth(float newMaxHealth, bool restoreFullHealth = true)
    {
        _maxHealth = Mathf.Max(1f, newMaxHealth);
        if (restoreFullHealth)
        {
            _currentHealth = _maxHealth;
        }
        else
        {
            _currentHealth = Mathf.Min(_currentHealth, _maxHealth);
        }
    }
    
    private Color GetHealthBarColor(float value)
    {
        float t = value / _maxHealth;
        return Color.Lerp(Color.red, Color.green, t);
    }
}