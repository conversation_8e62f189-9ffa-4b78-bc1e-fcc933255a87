fileFormatVersion: 2
guid: c0e5d31be11e242528ff7d2a0835bf8c
labels:
- Spritesheet
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: turn-0001
  - first:
      213: 21300002
    second: turn-0002
  - first:
      213: 21300004
    second: turn-0003
  - first:
      213: 21300006
    second: turn-0004
  - first:
      213: 21300008
    second: turn-0005
  - first:
      213: 21300010
    second: turn-0006
  - first:
      213: 21300012
    second: turn-0007
  - first:
      213: 21300014
    second: turn-0008
  - first:
      213: 21300016
    second: turn-0009
  - first:
      213: 21300018
    second: turn-0010
  - first:
      213: 21300020
    second: turn-0011
  - first:
      213: 21300022
    second: turn-0012
  - first:
      213: 21300024
    second: walk-0001
  - first:
      213: 21300026
    second: walk-0002
  - first:
      213: 21300028
    second: walk-0003
  - first:
      213: 21300030
    second: walk-0004
  - first:
      213: 21300032
    second: walk-0005
  - first:
      213: 21300034
    second: walk-0006
  - first:
      213: 21300036
    second: walk-0007
  - first:
      213: 21300038
    second: walk-0008
  - first:
      213: 21300040
    second: walk-0009
  - first:
      213: 21300042
    second: walk-0010
  - first:
      213: 21300044
    second: walk-0011
  - first:
      213: 21300046
    second: walk-0012
  - first:
      213: 21300048
    second: walk-0013
  - first:
      213: 21300050
    second: walk-0014
  - first:
      213: 21300052
    second: walk-0015
  - first:
      213: 21300054
    second: walk-0016
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  swizzle: 50462976
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: turn-0001
      rect:
        serializedVersion: 2
        x: 2
        y: 397
        width: 176
        height: 316
      alignment: 9
      pivot: {x: 0.4971591, y: -0.03164557}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 02305410000000000800000000000000
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: turn-0002
      rect:
        serializedVersion: 2
        x: 2
        y: 397
        width: 176
        height: 316
      alignment: 9
      pivot: {x: 0.4971591, y: -0.03164557}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 22305410000000000800000000000000
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: turn-0003
      rect:
        serializedVersion: 2
        x: 226
        y: 711
        width: 159
        height: 311
      alignment: 9
      pivot: {x: 0.4937107, y: -0.05466238}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 42305410000000000800000000000000
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: turn-0004
      rect:
        serializedVersion: 2
        x: 226
        y: 711
        width: 159
        height: 311
      alignment: 9
      pivot: {x: 0.4937107, y: -0.05466238}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62305410000000000800000000000000
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: turn-0005
      rect:
        serializedVersion: 2
        x: 869
        y: 423
        width: 133
        height: 314
      alignment: 9
      pivot: {x: 0.58270675, y: -0.0477707}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82305410000000000800000000000000
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: turn-0006
      rect:
        serializedVersion: 2
        x: 869
        y: 423
        width: 133
        height: 314
      alignment: 9
      pivot: {x: 0.58270675, y: -0.0477707}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a2305410000000000800000000000000
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: turn-0007
      rect:
        serializedVersion: 2
        x: 776
        y: 558
        width: 138
        height: 316
      alignment: 9
      pivot: {x: 0.7137681, y: -0.04113924}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2305410000000000800000000000000
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: turn-0008
      rect:
        serializedVersion: 2
        x: 776
        y: 558
        width: 138
        height: 316
      alignment: 9
      pivot: {x: 0.7137681, y: -0.04113924}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e2305410000000000800000000000000
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: turn-0009
      rect:
        serializedVersion: 2
        x: 538
        y: 354
        width: 137
        height: 302
      alignment: 9
      pivot: {x: 0.70437956, y: -0.049668875}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 03305410000000000800000000000000
      internalID: 21300016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: turn-0010
      rect:
        serializedVersion: 2
        x: 538
        y: 354
        width: 137
        height: 302
      alignment: 9
      pivot: {x: 0.70437956, y: -0.049668875}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 23305410000000000800000000000000
      internalID: 21300018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: turn-0011
      rect:
        serializedVersion: 2
        x: 338
        y: 447
        width: 156
        height: 312
      alignment: 9
      pivot: {x: 0.66346157, y: -0.044871796}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43305410000000000800000000000000
      internalID: 21300020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: turn-0012
      rect:
        serializedVersion: 2
        x: 338
        y: 447
        width: 156
        height: 312
      alignment: 9
      pivot: {x: 0.66346157, y: -0.044871796}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63305410000000000800000000000000
      internalID: 21300022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0001
      rect:
        serializedVersion: 2
        x: 86
        y: 29
        width: 166
        height: 320
      alignment: 9
      pivot: {x: 0.49096385, y: -0.0125}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 83305410000000000800000000000000
      internalID: 21300024
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0002
      rect:
        serializedVersion: 2
        x: 86
        y: 29
        width: 166
        height: 320
      alignment: 9
      pivot: {x: 0.49096385, y: -0.0125}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a3305410000000000800000000000000
      internalID: 21300026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0003
      rect:
        serializedVersion: 2
        x: 2
        y: 702
        width: 179
        height: 320
      alignment: 9
      pivot: {x: 0.5223464, y: -0.009375}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c3305410000000000800000000000000
      internalID: 21300028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0004
      rect:
        serializedVersion: 2
        x: 2
        y: 702
        width: 179
        height: 320
      alignment: 9
      pivot: {x: 0.5223464, y: -0.009375}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e3305410000000000800000000000000
      internalID: 21300030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0005
      rect:
        serializedVersion: 2
        x: 602
        y: 713
        width: 154
        height: 309
      alignment: 9
      pivot: {x: 0.4512987, y: -0.048543688}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04305410000000000800000000000000
      internalID: 21300032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0006
      rect:
        serializedVersion: 2
        x: 602
        y: 713
        width: 154
        height: 309
      alignment: 9
      pivot: {x: 0.4512987, y: -0.048543688}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24305410000000000800000000000000
      internalID: 21300034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0007
      rect:
        serializedVersion: 2
        x: 739
        y: 711
        width: 147
        height: 311
      alignment: 9
      pivot: {x: 0.39115646, y: -0.041800644}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44305410000000000800000000000000
      internalID: 21300036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0008
      rect:
        serializedVersion: 2
        x: 739
        y: 711
        width: 147
        height: 311
      alignment: 9
      pivot: {x: 0.39115646, y: -0.041800644}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 64305410000000000800000000000000
      internalID: 21300038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0009
      rect:
        serializedVersion: 2
        x: 465
        y: 482
        width: 147
        height: 317
      alignment: 9
      pivot: {x: 0.42517006, y: -0.02208202}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84305410000000000800000000000000
      internalID: 21300040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0010
      rect:
        serializedVersion: 2
        x: 465
        y: 482
        width: 147
        height: 317
      alignment: 9
      pivot: {x: 0.42517006, y: -0.02208202}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a4305410000000000800000000000000
      internalID: 21300042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0011
      rect:
        serializedVersion: 2
        x: 148
        y: 398
        width: 149
        height: 323
      alignment: 9
      pivot: {x: 0.45973155, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c4305410000000000800000000000000
      internalID: 21300044
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0012
      rect:
        serializedVersion: 2
        x: 148
        y: 398
        width: 149
        height: 323
      alignment: 9
      pivot: {x: 0.45973155, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e4305410000000000800000000000000
      internalID: 21300046
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0013
      rect:
        serializedVersion: 2
        x: 271
        y: 136
        width: 149
        height: 318
      alignment: 9
      pivot: {x: 0.43288592, y: -0.018867925}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 05305410000000000800000000000000
      internalID: 21300048
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0014
      rect:
        serializedVersion: 2
        x: 271
        y: 136
        width: 149
        height: 318
      alignment: 9
      pivot: {x: 0.43288592, y: -0.018867925}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 25305410000000000800000000000000
      internalID: 21300050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0015
      rect:
        serializedVersion: 2
        x: 2
        y: 86
        width: 165
        height: 322
      alignment: 9
      pivot: {x: 0.45757577, y: -0.0062111802}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 45305410000000000800000000000000
      internalID: 21300052
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: walk-0016
      rect:
        serializedVersion: 2
        x: 2
        y: 86
        width: 165
        height: 322
      alignment: 9
      pivot: {x: 0.45757577, y: -0.0062111802}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 65305410000000000800000000000000
      internalID: 21300054
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 6e9e3614d2c534291aaa44ba58ac4062
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      turn-0001: 21300000
      turn-0002: 21300002
      turn-0003: 21300004
      turn-0004: 21300006
      turn-0005: 21300008
      turn-0006: 21300010
      turn-0007: 21300012
      turn-0008: 21300014
      turn-0009: 21300016
      turn-0010: 21300018
      turn-0011: 21300020
      turn-0012: 21300022
      walk-0001: 21300024
      walk-0002: 21300026
      walk-0003: 21300028
      walk-0004: 21300030
      walk-0005: 21300032
      walk-0006: 21300034
      walk-0007: 21300036
      walk-0008: 21300038
      walk-0009: 21300040
      walk-0010: 21300042
      walk-0011: 21300044
      walk-0012: 21300046
      walk-0013: 21300048
      walk-0014: 21300050
      walk-0015: 21300052
      walk-0016: 21300054
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 16641
  packageName: TexturePacker Importer
  packageVersion: 7.6.0
  assetPath: Assets/codeandweb.com/Example/SpriteSheet/sprites.png
  uploadId: 729659
