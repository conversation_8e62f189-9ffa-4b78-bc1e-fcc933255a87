%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d3d0b52bf8c3e684f96ec4333fba0f67, type: 3}
  m_Name: ConcentratedEffect
  m_EditorClassIdentifier: 
  icon: {fileID: 21300000, guid: 3b4ab64c2a8444e4f8d8ae3f79d1b1d1, type: 3}
  gemName: Concentrated Effect
  description: Greatly increases damage but reduces area of effect
  rarity: 0
  level: 1
  maxLevel: 20
  modifierPool: {fileID: 0}
  useIntegerScaling: 0
  commonStatMultiplier: 0.7
  uncommonStatMultiplier: 0.85
  rareStatMultiplier: 1
  epicStatMultiplier: 1.15
  uniqueStatMultiplier: 1.3
  commonIntMultiplier: 0.5
  uncommonIntMultiplier: 0.75
  rareIntMultiplier: 1
  epicIntMultiplier: 1.5
  uniqueIntMultiplier: 2
  compatibleTags: 4
  damageIncreased: 0
  damageMore: 1.5
  cooldownMultiplier: 1
  manaCostMultiplier: 1
  attackSpeedMultiplier: 1
  addedCritChance: 0
  critMultiplierModifier: 1
  addsPierce: 0
  addsChain: 0
  addsAreaDamage: 1
  addsMultipleProjectiles: 0
  extraProjectiles: 2
  projectileSpreadAngle: 15
  chainCount: 2
  areaRadius: 1.5
  addsSpellEcho: 0
  echoDelay: 0.4
  echoCount: 1
  echoSpreadRadius: 0