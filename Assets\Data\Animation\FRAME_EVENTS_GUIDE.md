# Frame-Based Attack Timing Guide

## Overview
The attack system now supports precise frame-based timing using animation events. This allows attacks to trigger exactly when the animation visually shows the hit/shot moment.

## Example Configurations

### Melee Attack: `rat-attack.asset`
- **Frame Event**: Frame 4 (middle of 8-frame animation)
- **Event Name**: `hit`
- **Parameter**: `melee`
- **Triggers**: MeleeAttackStrategy damage dealing

### Ranged Attack: `rat-archer-attack.asset`
- **Frame Event**: Frame 7 (arrow release frame in 13-frame animation)
- **Event Name**: `shoot`
- **Parameter**: `ranged`
- **Triggers**: RangedAttackStrategy projectile spawning

## Supported Event Names

### Melee Attacks
- `hit` - Primary melee damage event
- `strike` - Alternative melee damage event
- `impact` - Alternative melee damage event

### Ranged Attacks
- `shoot` - Primary projectile spawn event
- `fire` - Alternative projectile spawn event
- `projectile` - Alternative projectile spawn event

## Fallback System
If no frame events are configured or triggered, attacks will execute automatically when the animation completes, ensuring backwards compatibility.

## Setting Up Frame Events
1. Open your SpriteAnimation asset in the inspector
2. Expand "Advanced Settings" → "Frame Events"
3. Add a new frame event:
   - **Frame Index**: The 0-based frame number to trigger
   - **Event Name**: One of the supported event names above
   - **Parameter**: Optional description (e.g., "melee", "ranged")

## Testing
Frame events are case-insensitive and will trigger the first time the specified frame is reached during an animation play.