%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8f2a8e88a54358f489cd8311c48805c2, type: 3}
  m_Name: DefaultSupportGemModifierPool
  m_EditorClassIdentifier: Assembly-CSharp::SupportGemModifierPool
  poolName: Default Support Gem Modifier Pool
  description: Standard modifier pool for all support gem drops. Contains balanced
    modifiers for general gameplay.
  commonModifierCount: 0
  uncommonModifierCount: 1
  rareModifierCount: 2
  epicModifierCount: 3
  uniqueModifierCount: 4
  minorModifiers:
  - type: 0
    minValue: 5
    maxValue: 15
    weight: 30
    tier: 0
  - type: 3
    minValue: 5
    maxValue: 10
    weight: 25
    tier: 0
  - type: 6
    minValue: 2
    maxValue: 5
    weight: 20
    tier: 0
  - type: 4
    minValue: 5
    maxValue: 10
    weight: 20
    tier: 0
  - type: 8
    minValue: 5
    maxValue: 10
    weight: 15
    tier: 0
  - type: 5
    minValue: 10
    maxValue: 20
    weight: 15
    tier: 0
  majorModifiers:
  - type: 0
    minValue: 15
    maxValue: 30
    weight: 25
    tier: 1
  - type: 1
    minValue: 10
    maxValue: 25
    weight: 15
    tier: 1
  - type: 3
    minValue: 10
    maxValue: 20
    weight: 20
    tier: 1
  - type: 6
    minValue: 5
    maxValue: 10
    weight: 20
    tier: 1
  - type: 7
    minValue: 10
    maxValue: 20
    weight: 15
    tier: 1
  - type: 4
    minValue: 10
    maxValue: 20
    weight: 20
    tier: 1
  - type: 10
    minValue: 1
    maxValue: 1
    weight: 10
    tier: 1
  epicModifiers:
  - type: 0
    minValue: 30
    maxValue: 50
    weight: 20
    tier: 2
  - type: 3
    minValue: 20
    maxValue: 35
    weight: 15
    tier: 2
  - type: 6
    minValue: 10
    maxValue: 15
    weight: 15
    tier: 2
  - type: 7
    minValue: 20
    maxValue: 40
    weight: 15
    tier: 2
  - type: 4
    minValue: 20
    maxValue: 30
    weight: 15
    tier: 2
  - type: 10
    minValue: 2
    maxValue: 2
    weight: 10
    tier: 2
  - type: 12
    minValue: 1
    maxValue: 2
    weight: 10
    tier: 2
  - type: 15
    minValue: 2
    maxValue: 5
    weight: 10
    tier: 2
  legendaryModifiers:
  - type: 0
    minValue: 50
    maxValue: 75
    weight: 15
    tier: 3
  - type: 3
    minValue: 35
    maxValue: 50
    weight: 10
    tier: 3
  - type: 7
    minValue: 40
    maxValue: 60
    weight: 10
    tier: 3
  - type: 10
    minValue: 3
    maxValue: 3
    weight: 8
    tier: 3
  - type: 12
    minValue: 2
    maxValue: 3
    weight: 8
    tier: 3
  - type: 2
    minValue: 30
    maxValue: 50
    weight: 10
    tier: 3
  - type: 13
    minValue: 25
    maxValue: 40
    weight: 10
    tier: 3
  - type: 15
    minValue: 5
    maxValue: 10
    weight: 8
    tier: 3
  tierWeightByRarity:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 1.5
      outSlope: 1.5
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 0.2
      value: 0.3
      inSlope: 1.25
      outSlope: 1.25
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 0.4
      value: 0.5
      inSlope: 0.9999999
      outSlope: 0.9999999
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 0.6
      value: 0.7
      inSlope: 0.875
      outSlope: 0.875
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0.75000006
      outSlope: 0.75000006
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
