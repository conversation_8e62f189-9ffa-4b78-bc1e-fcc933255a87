using System;
using System.Collections.Generic;

[Serializable]
public struct DamageInfo
{
    public float amount;
    public DamageType type;
    public bool isCritical;
    public float critMultiplier;
    public string source;
    public float ailmentChance;

    // Optional gem data for status effect configuration
    public SkillGemData skillGemData;
    public List<GemInstance> supportGems;

    public DamageInfo(float amount, DamageType type = DamageType.Physical)
    {
        this.amount = amount;
        this.type = type;
        this.isCritical = false;
        this.critMultiplier = 1f;
        this.source = string.Empty;
        this.ailmentChance = 0f;
        this.skillGemData = null;
        this.supportGems = null;
    }

    public DamageInfo(float amount, DamageType type, bool isCritical, float critMultiplier, string source = "", float ailmentChance = 0f)
    {
        this.amount = amount;
        this.type = type;
        this.isCritical = isCritical;
        this.critMultiplier = critMultiplier;
        this.source = source;
        this.ailmentChance = ailmentChance;
        this.skillGemData = null;
        this.supportGems = null;
    }

    public DamageInfo(float amount, DamageType type, bool isCritical, float critMultiplier, string source, float ailmentChance,
        SkillGemData skillGemData, List<GemInstance> supportGems = null)
    {
        this.amount = amount;
        this.type = type;
        this.isCritical = isCritical;
        this.critMultiplier = critMultiplier;
        this.source = source;
        this.ailmentChance = ailmentChance;
        this.skillGemData = skillGemData;
        this.supportGems = supportGems;
    }
}