%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d3d0b52bf8c3e684f96ec4333fba0f67, type: 3}
  m_Name: ElementalFocus
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Items.SupportGemData
  icon: {fileID: 0}
  gemName: Elemental Focus
  description: Greatly increases elemental damage
  rarity: 3
  level: 1
  maxLevel: 20
  damageMultiplier: 1.6
  cooldownMultiplier: 1
  manaCostMultiplier: 1.3
  addsPierce: 0
  addsChain: 0
  addsAreaDamage: 0
  chainCount: 2
  areaRadius: 3
  projectileSpreadAngle: 0
  forkCount: 2
  forkAngle: 30
  addsFork: 0
  igniteEffectivenessMultiplier: 1.5
  igniteDurationMultiplier: 1.3
  freezeEffectivenessMultiplier: 1.4
  freezeDurationMultiplier: 1.2
  bleedEffectivenessMultiplier: 1.3
  bleedDurationMultiplier: 1.1
  shockEffectivenessMultiplier: 1.6
  shockRangeMultiplier: 1.2
