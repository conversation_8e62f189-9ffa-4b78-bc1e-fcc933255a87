using UnityEngine;

/// <summary>
/// Bleed status effect that applies Physical damage over time.
/// Typically applied by Physical damage type attacks.
/// </summary>
public class BleedEffect : StatusEffect
{
    private float damagePerTick;
    private IDamageable targetHealth;
    
    public BleedEffect(float damagePerTick, float duration = 6f, string sourceId = "") 
        : base(StatusEffectType.Bleed, duration, 1f, sourceId)
    {
        this.damagePerTick = damagePerTick;
    }
    
    protected override void OnApply()
    {
        // Cache the health component
        if (target != null)
        {
            targetHealth = target.GetComponent<IDamageable>();
            if (targetHealth == null)
            {
                Debug.LogWarning($"BleedEffect applied to {target.name} but no IDamageable component found!");
            }
        }
    }
    
    protected override void OnTick()
    {
        if (targetHealth != null && target != null && target.activeInHierarchy)
        {
            // Create damage info for the tick damage
            DamageInfo tickDamage = new DamageInfo(
                damagePerTick,
                DamageType.Physical,
                false,
                1f,
                $"Bleed_DoT_{SourceId}"
            );
            
            targetHealth.TakeDamage(tickDamage);
        }
    }
    
    protected override void OnRemove()
    {
        // Cleanup if needed
        targetHealth = null;
    }
}
