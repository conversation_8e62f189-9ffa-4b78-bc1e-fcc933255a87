%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d3d0b52bf8c3e684f96ec4333fba0f67, type: 3}
  m_Name: GreaterMultipleProjectiles
  m_EditorClassIdentifier: 
  icon: {fileID: 21300000, guid: 3b4ab64c2a8444e4f8d8ae3f79d1b1d1, type: 3}
  gemName: Greater Multiple Projectiles
  description: Fires many projectiles in a wide spread pattern. Great area coverage but less focused damage
  rarity: 0
  level: 1
  maxLevel: 20
  modifierPool: {fileID: 0}
  useIntegerScaling: 1
  commonStatMultiplier: 0.7
  uncommonStatMultiplier: 0.85
  rareStatMultiplier: 1
  epicStatMultiplier: 1.15
  uniqueStatMultiplier: 1.3
  commonIntMultiplier: 2
  uncommonIntMultiplier: 3
  rareIntMultiplier: 4
  epicIntMultiplier: 5
  uniqueIntMultiplier: 6
  compatibleTags: 2
  damageIncreased: -30
  damageMore: 1
  cooldownMultiplier: 1
  manaCostMultiplier: 1.5
  attackSpeedMultiplier: 1
  addedCritChance: 0
  critMultiplierModifier: 1
  addsPierce: 0
  addsChain: 0
  addsAreaDamage: 0
  addsMultipleProjectiles: 1
  extraProjectiles: 4
  projectileSpreadAngle: 25
  chainCount: 2
  areaRadius: 3
  addsSpellEcho: 0
  echoDelay: 0.4
  echoCount: 1
  echoSpreadRadius: 0