using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Shock status effect that can chain lightning damage to nearby enemies.
/// Typically applied by Lightning damage type attacks.
/// </summary>
public class ShockEffect : StatusEffect
{
    private float chainDamage;
    private float chainRange;
    private IDamageable targetHealth;
    
    public ShockEffect(float chainDamage, float chainRange = 3f, float duration = 2f, string sourceId = "") 
        : base(StatusEffectType.Shock, duration, 0.5f, sourceId)
    {
        this.chainDamage = chainDamage;
        this.chainRange = chainRange;
    }
    
    protected override void OnApply()
    {
        // Cache the health component
        if (target != null)
        {
            targetHealth = target.GetComponent<IDamageable>();
            if (targetHealth == null)
            {
                Debug.LogWarning($"ShockEffect applied to {target.name} but no IDamageable component found!");
            }
        }
    }
    
    protected override void OnTick()
    {
        if (target != null && target.activeInHierarchy)
        {
            // Chain lightning to nearby enemies
            ChainToNearbyEnemies();
        }
    }
    
    protected override void OnRemove()
    {
        // Cleanup if needed
        targetHealth = null;
    }
    
    private void ChainToNearbyEnemies()
    {
        if (target == null) return;
        
        // Find nearby enemies within chain range
        Collider2D[] nearbyColliders = Physics2D.OverlapCircleAll(target.transform.position, chainRange);
        List<GameObject> validTargets = new List<GameObject>();
        
        foreach (var collider in nearbyColliders)
        {
            // Skip the original target
            if (collider.gameObject == target) continue;
            
            // Check if the target has a health component and is not the player
            IDamageable damageable = collider.GetComponent<IDamageable>();
            if (damageable != null)
            {
                // Simple check to avoid chaining to player - you may need to adjust this based on your tag/layer system
                if (!collider.CompareTag("Player"))
                {
                    validTargets.Add(collider.gameObject);
                }
            }
        }
        
        // Chain to up to 2 nearby enemies per tick
        int chainCount = Mathf.Min(2, validTargets.Count);
        for (int i = 0; i < chainCount; i++)
        {
            GameObject chainTarget = validTargets[i];
            IDamageable chainTargetHealth = chainTarget.GetComponent<IDamageable>();
            
            if (chainTargetHealth != null)
            {
                // Create chain damage info
                DamageInfo chainDamageInfo = new DamageInfo(
                    chainDamage,
                    DamageType.Lightning,
                    false,
                    1f,
                    $"Shock_Chain_{SourceId}"
                );
                
                chainTargetHealth.TakeDamage(chainDamageInfo);
                
                // Visual effect could be added here
                Debug.Log($"Shock chained from {target.name} to {chainTarget.name} for {chainDamage:F1} damage");
            }
        }
    }
}
