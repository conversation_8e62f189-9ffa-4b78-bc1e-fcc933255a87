using UnityEngine;
using Sirenix.OdinInspector;
using System.Linq;

/// <summary>
/// Test script to validate the status effect system improvements.
/// This script can be attached to a GameObject in the scene to test the new functionality.
/// </summary>
public class StatusEffectSystemTest : MonoBehaviour
{
    [Title("Status Effect System Test")]
    [InfoBox("This script tests the new status effect system improvements including:\n" +
             "• StatusEffectHelper centralized logic\n" +
             "• Gem-based status effect configuration\n" +
             "• Enhanced tooltips with status effect information")]
    
    [Title("Test Configuration")]
    [Required]
    [AssetsOnly]
    [Tooltip("Skill gem to test status effect configuration")]
    public SkillGemData testSkillGem;
    
    [Required]
    [AssetsOnly]
    [Tooltip("Support gem to test status effect modifiers")]
    public SupportGemData testSupportGem;
    
    [Range(1f, 100f)]
    [Tooltip("Test damage amount for status effect calculations")]
    public float testDamage = 50f;
    
    [Range(0f, 100f)]
    [Tooltip("Test ailment chance percentage")]
    public float testAilmentChance = 25f;
    
    [Title("Test Results")]
    [ShowInInspector]
    [ReadOnly]
    [MultiLineProperty(10)]
    [Tooltip("Generated tooltip text from the skill gem")]
    public string skillGemTooltip;
    
    [ShowInInspector]
    [ReadOnly]
    [MultiLineProperty(10)]
    [Tooltip("Generated tooltip text from the support gem")]
    public string supportGemTooltip;
    
    [ShowInInspector]
    [ReadOnly]
    [MultiLineProperty(5)]
    [Tooltip("Status effect configuration from the skill gem")]
    public string statusEffectConfig;
    
    [Title("Test Actions")]
    [Button("Test Skill Gem Tooltip")]
    [InfoBox("Tests the enhanced skill gem tooltip with status effect information")]
    public void TestSkillGemTooltip()
    {
        if (testSkillGem == null)
        {
            Debug.LogError("Test Skill Gem is not assigned!");
            return;
        }

        skillGemTooltip = testSkillGem.GetTooltipText();
        Debug.Log($"Skill Gem Tooltip:\n{skillGemTooltip}");

        // Also test with a specific gem for demonstration
        var fireball = UnityEngine.Resources.LoadAll<SkillGemData>("").FirstOrDefault(g => g.name == "Fireball");
        if (fireball != null)
        {
            Debug.Log($"Fireball Tooltip:\n{fireball.GetTooltipText()}");
        }
    }
    
    [Button("Test Support Gem Tooltip")]
    [InfoBox("Tests the enhanced support gem tooltip with status effect modifiers")]
    public void TestSupportGemTooltip()
    {
        if (testSupportGem == null)
        {
            Debug.LogError("Test Support Gem is not assigned!");
            return;
        }
        
        supportGemTooltip = testSupportGem.GetTooltipText();
        Debug.Log($"Support Gem Tooltip:\n{supportGemTooltip}");
    }
    
    [Button("Test Status Effect Configuration")]
    [InfoBox("Tests the gem-based status effect configuration system")]
    public void TestStatusEffectConfiguration()
    {
        if (testSkillGem == null)
        {
            Debug.LogError("Test Skill Gem is not assigned!");
            return;
        }
        
        var config = testSkillGem.GetStatusEffectConfig();
        statusEffectConfig = $"Ignite: {config.ignitePercent * 100:F1}% over {config.igniteDuration:F1}s\n" +
                           $"Freeze: {config.freezeSlowAmount * 100:F1}% slow for {config.freezeDuration:F1}s\n" +
                           $"Bleed: {config.bleedPercent * 100:F1}% over {config.bleedDuration:F1}s\n" +
                           $"Shock: {config.shockChainDamage * 100:F1}% chain, {config.shockChainRange:F1}m range for {config.shockDuration:F1}s";
        
        Debug.Log($"Status Effect Configuration:\n{statusEffectConfig}");
    }
    
    [Button("Test StatusEffectHelper")]
    [InfoBox("Tests the centralized StatusEffectHelper logic")]
    public void TestStatusEffectHelper()
    {
        if (testSkillGem == null)
        {
            Debug.LogError("Test Skill Gem is not assigned!");
            return;
        }
        
        // Create test damage info
        var damageInfo = new DamageInfo(
            testDamage,
            testSkillGem.damageType,
            false,
            1f,
            "Test",
            testAilmentChance,
            testSkillGem,
            null
        );
        
        // Test with default configuration
        Debug.Log($"Testing StatusEffectHelper with {testDamage} {testSkillGem.damageType} damage, {testAilmentChance}% ailment chance");
        Debug.Log("Note: This test only validates the helper logic - no actual status effects will be applied without a target.");
        
        // Test configuration retrieval
        var config = testSkillGem.GetStatusEffectConfig();
        Debug.Log($"Retrieved configuration: Ignite {config.ignitePercent * 100:F1}%, Freeze {config.freezeSlowAmount * 100:F1}% slow");
    }
    
    [Button("Test Support Gem Modifiers")]
    [InfoBox("Tests how support gems modify status effect configurations")]
    public void TestSupportGemModifiers()
    {
        if (testSkillGem == null || testSupportGem == null)
        {
            Debug.LogError("Both Test Skill Gem and Test Support Gem must be assigned!");
            return;
        }
        
        // Get base configuration
        var baseConfig = testSkillGem.GetStatusEffectConfig();
        
        // Apply support gem modifiers
        var modifiedConfig = testSupportGem.ApplyStatusEffectModifiers(baseConfig);
        
        Debug.Log("Support Gem Modifier Test:");
        Debug.Log($"Base Ignite: {baseConfig.ignitePercent * 100:F1}% → Modified: {modifiedConfig.ignitePercent * 100:F1}%");
        Debug.Log($"Base Freeze: {baseConfig.freezeSlowAmount * 100:F1}% → Modified: {modifiedConfig.freezeSlowAmount * 100:F1}%");
        Debug.Log($"Base Bleed: {baseConfig.bleedPercent * 100:F1}% → Modified: {modifiedConfig.bleedPercent * 100:F1}%");
        Debug.Log($"Base Shock: {baseConfig.shockChainDamage * 100:F1}% → Modified: {modifiedConfig.shockChainDamage * 100:F1}%");
    }
    
    [Button("Run All Tests")]
    [InfoBox("Runs all status effect system tests")]
    public void RunAllTests()
    {
        Debug.Log("=== Status Effect System Test Suite ===");
        
        TestSkillGemTooltip();
        TestSupportGemTooltip();
        TestStatusEffectConfiguration();
        TestStatusEffectHelper();
        TestSupportGemModifiers();
        
        Debug.Log("=== Test Suite Complete ===");
    }
    
    private void OnValidate()
    {
        // Auto-update tooltips when gems are changed in the inspector
        if (testSkillGem != null)
        {
            skillGemTooltip = testSkillGem.GetTooltipText();
        }
        
        if (testSupportGem != null)
        {
            supportGemTooltip = testSupportGem.GetTooltipText();
        }
    }
}
