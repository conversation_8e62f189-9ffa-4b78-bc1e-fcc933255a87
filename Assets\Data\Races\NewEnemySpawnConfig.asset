%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 878abae2124be5b479b8d1fe0ee611ee, type: 3}
  m_Name: NewEnemySpawnConfig
  m_EditorClassIdentifier: Assembly-CSharp::EnemySpawnConfiguration
  configurationName: Default Enemy Spawn Config
  description: 
  enemyRaces:
  - raceData: {fileID: 11400000, guid: 3414985995f079046991f0a3a86dbd2f, type: 2}
    weight: 1
    minChunkLevel: 1
    maxChunkLevel: 100
  minEnemiesPerChunk: 2
  maxEnemiesPerChunk: 6
  multiRaceSpawnChance: 0.2
  enemySpawnChance: 0.7
  enemyCountScaling: 0.1
  maxScalingBonus: 3
  useDistanceBasedScaling: 1
