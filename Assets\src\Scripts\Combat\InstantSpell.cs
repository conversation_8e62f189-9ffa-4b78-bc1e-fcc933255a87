using UnityEngine;
using Sirenix.OdinInspector;
using System.Collections;
using System.Collections.Generic;

[RequireComponent(typeof(SpatialCollider))]
public class InstantSpell : MonoBehaviour, ISpawnable, ISpatialCollisionHandler
{
    public enum SpellMode
    {
        Instant,    // Deal damage once to all in radius
        DamageOverTime  // Deal damage periodically
    }

    [Title("Spell Settings")]
    [SerializeField] private SpellMode spellMode = SpellMode.Instant;
    [SerializeField] private float baseDamage = 20f;    
    [ShowIf("spellMode", SpellMode.DamageOverTime)]
    [SerializeField] private float duration = 5f;
    
    [ShowIf("spellMode", SpellMode.DamageOverTime)]
    [SerializeField] private float tickInterval = 0.5f;
    
    [ShowIf("spellMode", SpellMode.DamageOverTime)]
    [SerializeField] private bool damageOnEntry = true;
    
    [Title("Visual Settings")]
    [SerializeField] private float visualEffectDuration = 1f;
    
    [Title("Delay Settings")]
    [SerializeField] private bool useDelay = false;
    [ShowIf("useDelay")]
    [SerializeField, Range(0.1f, 5f)] private float delayDuration = 1.5f;
    [ShowIf("useDelay")]
    [SerializeField] private int delayWarningParticleCount = 30;
    
    [Title("Particle Effects")]
    [SerializeField] private bool useSpawnParticles = true;
    [ShowIf("useSpawnParticles")]
    [SerializeField] private ParticleType spawnParticleType = ParticleType.MagicAura;
    [ShowIf("useSpawnParticles")]
    [SerializeField] private int spawnParticleCount = 20;
    
    [ShowIf("spellMode", SpellMode.DamageOverTime)]
    [SerializeField] private bool useContinuousParticles = true;
    [ShowIf("@spellMode == SpellMode.DamageOverTime && useContinuousParticles")]
    [SerializeField] private ParticleType continuousParticleType = ParticleType.PoisonCloud;
    [ShowIf("@spellMode == SpellMode.DamageOverTime && useContinuousParticles")]
    [SerializeField] private float particleInterval = 0.5f;
    
    [SerializeField] private bool useDespawnParticles = true;
    [ShowIf("useDespawnParticles")]
    [SerializeField] private ParticleType despawnParticleType = ParticleType.SmokeImpact;
    [ShowIf("useDespawnParticles")]
    [SerializeField] private int despawnParticleCount = 10;
    
    [Title("Debug Gizmos")]
    [SerializeField] private bool showGizmos = true;
    [ShowIf("showGizmos")]
    [SerializeField] private Color gizmoColor = Color.red;    
    // Runtime values
    public float damage { get; set; }
    public float critChance { get; set; }
    public float critMultiplier { get; set; }
    public DamageType damageType { get; set; } = DamageType.Physical;
    public float ailmentChance { get; set; } = 0f;

    // Gem data for status effect configuration
    public SkillGemData skillGemData { get; set; }
    public System.Collections.Generic.List<GemInstance> supportGems { get; set; }
    private CollisionLayers _targetLayer;
    private SpatialCollider _spatialCollider;
    private HashSet<GameObject> _damagedTargets;
    private HashSet<GameObject> _targetsInArea;
    private Coroutine _dotCoroutine;
    private float _spawnTime;
    private bool _isActive;
    private bool _damageEnabled = true;
    
    private void Awake()
    {
        _spatialCollider = GetComponent<SpatialCollider>();
        _damagedTargets = new HashSet<GameObject>();
        _targetsInArea = new HashSet<GameObject>();
        
        // Configure spatial collider for area detection
        _spatialCollider.shape = ColliderShape.Circle;
        _spatialCollider.SetTrigger(true);    }
    
    public void Initialize(Vector2 position, float damageMultiplier = 1f, CollisionLayers layer = CollisionLayers.PlayerProjectile)
    {
        transform.position = position;
        damage = baseDamage * damageMultiplier;
        _targetLayer = layer;
        _isActive = true;
        _spawnTime = Time.time;
        
        // Set collision layer
        _spatialCollider.SetLayer(layer);
        
        // Visual effects keep their original scale
        
        // Handle delay if enabled
        if (useDelay)
        {
            _damageEnabled = false;
            StartCoroutine(DelayedActivation());
            
            // Spawn warning particles immediately
            if (ParticleEffectManager.Instance != null)
            {
                ParticleEffectManager.Instance.SpawnParticle(ParticleType.SpellWarning, transform.position, delayWarningParticleCount);
            }
        }
        else
        {
            _damageEnabled = true;
            
            // Spawn initial particles
            if (useSpawnParticles && ParticleEffectManager.Instance != null)
            {
                ParticleEffectManager.Instance.SpawnParticle(spawnParticleType, transform.position, spawnParticleCount);
            }
            
            // Handle DoT mode (instant damage is now processed via trigger events)
            if (spellMode == SpellMode.DamageOverTime)
            {
                if (_dotCoroutine != null)
                {
                    StopCoroutine(_dotCoroutine);
                }
                _dotCoroutine = StartCoroutine(DamageOverTimeRoutine());
                
                // Start continuous particles for DoT
                if (useContinuousParticles && ParticleEffectManager.Instance != null)
                {
                    ParticleEffectManager.Instance.StartContinuousEffect(continuousParticleType, transform, particleInterval);
                }
            }
        }
        
        // Schedule despawn after visual effect duration
        StartCoroutine(DespawnAfterVisual());
    }
    
    private IEnumerator DelayedActivation()
    {
        yield return new WaitForSeconds(delayDuration);
        
        _damageEnabled = true;
        
        // Spawn actual spell particles after delay
        if (useSpawnParticles && ParticleEffectManager.Instance != null)
        {
            ParticleEffectManager.Instance.SpawnParticle(spawnParticleType, transform.position, spawnParticleCount);
        }
        
        // Handle instant damage to all targets currently in area
        if (spellMode == SpellMode.Instant)
        {
            ApplyInstantDamageToAllInArea();
        }
        // Start DoT mode after delay
        else if (spellMode == SpellMode.DamageOverTime)
        {
            if (_dotCoroutine != null)
            {
                StopCoroutine(_dotCoroutine);
            }
            _dotCoroutine = StartCoroutine(DamageOverTimeRoutine());
            
            // Start continuous particles for DoT
            if (useContinuousParticles && ParticleEffectManager.Instance != null)
            {
                ParticleEffectManager.Instance.StartContinuousEffect(continuousParticleType, transform, particleInterval);
            }
        }
    }
    
    private IEnumerator DamageOverTimeRoutine()
    {
        float elapsedTime = 0f;
        
        while (elapsedTime < duration && _isActive)
        {
        // Apply damage tick
        ApplyDamageToTargetsInArea();
        
        yield return new WaitForSeconds(tickInterval);
        elapsedTime += tickInterval;
        }
        
        _dotCoroutine = null;
    }
    
    // Iterate over every target currently inside the spell area and apply damage
    private void ApplyDamageToTargetsInArea()
    {
        if (_targetsInArea.Count == 0) return;

        // Copy to temporary list to avoid modification during iteration
        var targets = new List<GameObject>(_targetsInArea);
        foreach (var target in targets)
        {
        if (target == null) continue;
        if (ShouldDamageTarget(target))
        {
            ApplyDamageToTarget(target);
        }
        }
    }
    
    private bool ShouldDamageTarget(GameObject target)
    {
        // Obtain SpatialCollider without using GetComponent at runtime
        SpatialCollider targetCollider = null;

        // Use cached player SpatialCollider if this is the player
        if (target == PlayerManager.PlayerGameObject)
        {
            targetCollider = PlayerManager.PlayerSpatialCollider;
        }
        else if (PoolManager.Instance != null)
        {
            // Retrieve cached component from pool
            PoolManager.Instance.GetCachedComponent<SpatialCollider>(target, out targetCollider);
        }

        if (targetCollider == null)
            return false;

        // Check if we should damage this target based on our layer settings
        if (_targetLayer == CollisionLayers.PlayerProjectile)
        {
            // Player spells damage enemies
            return targetCollider.Layer.HasFlag(CollisionLayers.Enemy);
        }
        else if (_targetLayer == CollisionLayers.EnemyProjectile)
        {
            // Enemy spells damage player
            return targetCollider.Layer.HasFlag(CollisionLayers.Player);
        }

        return false;
    }
    
    private void ApplyDamageToTarget(GameObject target)
    {
        // Calculate final damage with crit
        float finalDamage = damage;
        bool isCrit = Random.Range(0f, 100f) < critChance;
        if (isCrit)
        {
            finalDamage *= critMultiplier;
        }
        
        // Create damage info with gem data for status effect configuration
        DamageInfo damageInfo = new DamageInfo(
            Mathf.RoundToInt(finalDamage),
            damageType,
            isCrit,
            critMultiplier,
            "InstantSpell",
            ailmentChance,
            skillGemData,
            supportGems
        );
        
        // Handle player damage via PlayerManager when appropriate
        if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
        {
        PlayerManager.DealDamageToPlayer(damageInfo);
        return;
        }

        // Fall back to pooled or direct HealthComponent lookup for all other targets
        if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
        {
        healthComponent.TakeDamage(damageInfo);
        }
        else if (target.TryGetComponent<HealthComponent>(out var directHealth))
        {
        directHealth.TakeDamage(damageInfo);
        }
    }
    
    private void ApplyInstantDamageToAllInArea()
    {
        // Apply damage to all targets currently in the area
        var targets = new List<GameObject>(_targetsInArea);
        foreach (var target in targets)
        {
            if (target != null && ShouldDamageTarget(target) && !_damagedTargets.Contains(target))
            {
                ApplyDamageToTarget(target);
                _damagedTargets.Add(target);
            }
        }
    }
    
    private IEnumerator DespawnAfterVisual()
    {
        // Wait for visual effect duration
        yield return new WaitForSeconds(visualEffectDuration);
        
        // Only despawn if we're not in DoT mode or DoT has finished
        if (spellMode == SpellMode.Instant || 
        (spellMode == SpellMode.DamageOverTime && Time.time - _spawnTime >= duration))
        {
        Deactivate();
        }
        else
        {
        // For DoT, wait until duration is complete
        float remainingTime = duration - (Time.time - _spawnTime);
        if (remainingTime > 0)
        {
            yield return new WaitForSeconds(remainingTime);
        }
        Deactivate();
        }
    }
    
    private void Deactivate()
    {
        _isActive = false;
        
        if (_dotCoroutine != null)
        {
        StopCoroutine(_dotCoroutine);
        _dotCoroutine = null;
        }
        
        // Stop continuous particles if active
        if (useContinuousParticles && ParticleEffectManager.Instance != null)
        {
        ParticleEffectManager.Instance.StopContinuousEffect(transform);
        }
        
        // Spawn despawn particles
        if (useDespawnParticles && ParticleEffectManager.Instance != null)
        {
        ParticleEffectManager.Instance.SpawnParticle(despawnParticleType, transform.position, despawnParticleCount);
        }
        
        if (PoolManager.Instance != null)
        {
        PoolManager.Instance.Despawn(gameObject);
        }
        else
        {
        gameObject.SetActive(false);
        }
    }
    
    // ISpatialCollisionHandler implementation
    public void HandleCollisionEnter(CollisionInfo collision) { }
    public void HandleCollisionStay(CollisionInfo collision) { }
    public void HandleCollisionExit(CollisionInfo collision) { }
    
    public void HandleTriggerEnter(CollisionInfo collision)
    {
        var obj = collision.Other.GameObject;

        // Track objects currently inside the area
        _targetsInArea.Add(obj);

        if (!ShouldDamageTarget(obj)) return;

        // Only apply damage if damage is enabled (not during delay)
        if (!_damageEnabled) return;

        bool shouldApplyNow = false;
        if (spellMode == SpellMode.Instant)
        {
        shouldApplyNow = true;
        }
        else if (spellMode == SpellMode.DamageOverTime && damageOnEntry)
        {
        shouldApplyNow = true;
        }

        if (shouldApplyNow && !_damagedTargets.Contains(obj))
        {
        ApplyDamageToTarget(obj);
        _damagedTargets.Add(obj);
        }
    }
    
    public void HandleTriggerStay(CollisionInfo collision) { }
    
    public void HandleTriggerExit(CollisionInfo collision)
    {
        // Remove from damaged targets when they leave (for DoT re-entry damage)
        _targetsInArea.Remove(collision.Other.GameObject);

        if (spellMode == SpellMode.DamageOverTime && damageOnEntry)
        {
        _damagedTargets.Remove(collision.Other.GameObject);
        }
    }
    
    // ISpawnable implementation
    public void OnSpawn()
    {
        _isActive = true;
        _damagedTargets.Clear();
        _targetsInArea.Clear();
    }
    
    public void OnDespawn()
    {
        _isActive = false;
        damage = baseDamage; // Reset to base damage
        _damageEnabled = true; // Reset damage enabled state
        _damagedTargets.Clear();
        _targetsInArea.Clear();
        
        if (_dotCoroutine != null)
        {
        StopCoroutine(_dotCoroutine);
        _dotCoroutine = null;
        }
        
        // Ensure particles are stopped
        if (ParticleEffectManager.Instance != null)
        {
        ParticleEffectManager.Instance.StopContinuousEffect(transform);
        }
        
        // Stop all coroutines to ensure clean state
        StopAllCoroutines();
    }
    
    // Editor helpers
    private void OnDrawGizmos()
    {
        if (!showGizmos || _spatialCollider == null) return;
        
        // Draw spell radius
        Gizmos.color = gizmoColor;
        Gizmos.DrawWireSphere(transform.position, _spatialCollider.radius);
        
        // Draw filled circle with transparency for better visualization
        Color fillColor = gizmoColor;
        fillColor.a = 0.1f;
        Gizmos.color = fillColor;
        Gizmos.DrawSphere(transform.position, _spatialCollider.radius);
    }
    
    private void OnDrawGizmosSelected()
    {
        if (!showGizmos || _spatialCollider == null) return;
        
        // Draw more detailed gizmos when selected
        Gizmos.color = gizmoColor;
        Gizmos.DrawWireSphere(transform.position, _spatialCollider.radius);
    }
}