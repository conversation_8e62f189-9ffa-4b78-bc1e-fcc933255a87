using UnityEngine;
using Sirenix.OdinInspector;

[CreateAssetMenu(fileName = "New Skill Gem", menuName = "2D Rogue/Items/Skill Gem")]
public class SkillGemData : GemData
{
    [Title("Icon Setup")]
    [InfoBox("Choose between single icon or layered icons (background + foreground). Layered icons provide better visual depth.")]
    [<PERSON><PERSON>("Clear Single Icon")]
    private void ClearSingleIcon() { icon = null; }
    
    [<PERSON><PERSON>("Clear Layered Icons")]  
    private void ClearLayeredIcons() { backgroundIcon = null; foregroundIcon = null; }
    
    [Title("Skill Type")]
    [EnumToggleButtons]
    public SkillType skillType = SkillType.Projectile;
    
    [Title("Gem Tags")]
    [InfoBox("Select which tags this skill gem has. Support gems will only work with matching tags.")]
    [EnumToggleButtons]
    public GemTag gemTags = GemTag.Projectile;
    
    [Title("Skill Properties")]
    [Required("A skill prefab is required for spawning the skill effect")]
    [AssetSelector]
    public GameObject skillPrefab;
    
    public float baseDamage = 10f;
    
    [Range(0.1f, 5f)]
    public float cooldown = 1f;
    
    [Range(0f, 50f)]
    public float manaCost = 10f;
    
    [Title("Projectile Properties")]
    [ShowIf("skillType", SkillType.Projectile)]
    [Range(1f, 50f)]
    public float projectileSpeed = 10f;
    
    [ShowIf("skillType", SkillType.Projectile)]
    [Range(0.1f, 10f)]
    [Tooltip("How long the projectile lives before despawning")]
    public float duration = 2f;
    
    [Title("Instant Skill Properties")]
    [ShowIf("skillType", SkillType.Instant)]
    [InfoBox("If true, skill spawns at target position. If false, spawns at player position.")]
    public bool targetGroundPosition = true;
    
    [Title("Combat Stats")]
    [Range(0.1f, 2f)]
    [Tooltip("Attack speed multiplier for this skill")]
    public float attackSpeedMultiplier = 1f;
    
    [Range(0f, 100f)]
    [Tooltip("Critical strike chance percentage")]
    public float critChance = 5f;
    
    [Range(1f, 5f)]
    [Tooltip("Critical strike damage multiplier")]
    public float critMultiplier = 2f;
    
    [Title("Damage Type")]
    [EnumToggleButtons]
    public DamageType damageType = DamageType.Physical;
    
    [Title("Ailment Settings")]
    [Range(0f, 100f)]
    [Tooltip("Base chance to inflict ailments (ignite for fire, bleed for physical, etc.)")]
    public float ailmentChance = 10f;

    [Title("Status Effect Configuration")]
    [InfoBox("Configure status effect parameters for this skill. These values override defaults when this skill applies status effects.")]

    [FoldoutGroup("Ignite (Fire)")]
    [Range(0.05f, 0.5f)]
    [Tooltip("Percentage of damage dealt as ignite DoT (default: 0.2 = 20%)")]
    public float ignitePercent = 0.2f;

    [FoldoutGroup("Ignite (Fire)")]
    [Range(1f, 10f)]
    [Tooltip("Duration of ignite effect in seconds")]
    public float igniteDuration = 4f;

    [FoldoutGroup("Freeze (Ice)")]
    [Range(0.1f, 1f)]
    [Tooltip("Movement speed reduction amount (0.5 = 50% slower)")]
    public float freezeSlowAmount = 0.5f;

    [FoldoutGroup("Freeze (Ice)")]
    [Range(0.5f, 5f)]
    [Tooltip("Duration of freeze effect in seconds")]
    public float freezeDuration = 2f;

    [FoldoutGroup("Bleed (Physical)")]
    [Range(0.05f, 0.3f)]
    [Tooltip("Percentage of damage dealt as bleed DoT (default: 0.15 = 15%)")]
    public float bleedPercent = 0.15f;

    [FoldoutGroup("Bleed (Physical)")]
    [Range(2f, 10f)]
    [Tooltip("Duration of bleed effect in seconds")]
    public float bleedDuration = 6f;

    [FoldoutGroup("Shock (Lightning)")]
    [Range(0.05f, 0.25f)]
    [Tooltip("Percentage of damage for chain lightning (default: 0.1 = 10%)")]
    public float shockChainDamage = 0.1f;

    [FoldoutGroup("Shock (Lightning)")]
    [Range(1f, 8f)]
    [Tooltip("Range for chain lightning in units")]
    public float shockChainRange = 3f;

    [FoldoutGroup("Shock (Lightning)")]
    [Range(0.5f, 5f)]
    [Tooltip("Duration of shock effect in seconds")]
    public float shockDuration = 2f;
    
    [Title("Support Gem Slots")]
    [ReadOnly]
    [ShowInInspector]
    [PropertySpace]
    [InfoBox("Support slots are determined by rarity:\nCommon: 0, Uncommon: 1, Rare: 2, Epic: 3, Unique: 5")]
    public int SupportSlotCount => (int)rarity;

    /// <summary>
    /// Get the status effect configuration for this skill gem
    /// </summary>
    public StatusEffectHelper.StatusEffectConfig GetStatusEffectConfig()
    {
        return new StatusEffectHelper.StatusEffectConfig
        {
            ignitePercent = this.ignitePercent,
            igniteDuration = this.igniteDuration,
            igniteTickInterval = 0.5f, // Fixed tick interval

            freezeSlowAmount = this.freezeSlowAmount,
            freezeDuration = this.freezeDuration,

            bleedPercent = this.bleedPercent,
            bleedDuration = this.bleedDuration,
            bleedTickInterval = 1f, // Fixed tick interval

            shockChainDamage = this.shockChainDamage,
            shockChainRange = this.shockChainRange,
            shockDuration = this.shockDuration
        };
    }
    
    public override string GetTooltipText()
    {
        string tooltip = $"<color=#{ColorUtility.ToHtmlStringRGB(GetRarityColor())}>{gemName}</color>\n" +
           $"Type: {skillType}";
           
        // Add gem tags display
        if (gemTags != GemTag.None)
        {
            var tagNames = new System.Collections.Generic.List<string>();
            if ((gemTags & GemTag.Melee) != 0) tagNames.Add(GemTag.Melee.GetColoredDisplayName());
            if ((gemTags & GemTag.Projectile) != 0) tagNames.Add(GemTag.Projectile.GetColoredDisplayName());
            if ((gemTags & GemTag.Spell) != 0) tagNames.Add(GemTag.Spell.GetColoredDisplayName());
            tooltip += $"\nTags: {string.Join(", ", tagNames)}";
        }
        
        tooltip += $"\n\n{description}\n\n" +
           $"Damage: {baseDamage} ({damageType})\n" +
           $"Cooldown: {cooldown}s\n" +
           $"Mana Cost: {manaCost}\n" +
           $"Attack Speed: {attackSpeedMultiplier:F1}x\n" +
           $"Critical Chance: {critChance:F1}%\n" +
           $"Critical Multiplier: {critMultiplier:F1}x\n" +
           $"Support Slots: {SupportSlotCount}";
        
        if (skillType == SkillType.Projectile)
        {
        tooltip += $"\n\nProjectile Speed: {projectileSpeed}\n" +
              $"Duration: {duration}s";
        }
        else if (skillType == SkillType.Instant && targetGroundPosition)
        {
        tooltip += "\n\nTargets ground position";
        }
        
        return tooltip;
    }
}