using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem;
using Sirenix.OdinInspector;
using System.Collections;

[System.Serializable]
public class SpellEchoData
{
    public int slotIndex;
    public int remainingEchoes;
    public Vector3 targetPosition;
    public GemSocketController controller;
    public SkillGemData skillData;
    public float echoDelay;
    public float echoSpreadRadius;
}

public class SkillExecutor : MonoBehaviour
{
    [Title("References")]
    [SerializeField]
    private EquipmentPanel equipmentPanel;
    
    [SerializeField]
    private PlayerStats playerStats;
    
    [Title("Skill State")]
    [ShowInInspector, ReadOnly]
    private Dictionary<int, float> skillCooldowns = new Dictionary<int, float>();
    
    [ShowInInspector, ReadOnly]
    private Dictionary<int, GemSocketController> cachedControllers = new Dictionary<int, GemSocketController>();
    
    [Title("Spell Echo State")]
    [ShowInInspector, ReadOnly]
    private List<Coroutine> activeSpellEchoes = new List<Coroutine>();
    
    private Camera mainCamera;
    
    private void Awake()
    {
        if (equipmentPanel == null)
            equipmentPanel = FindFirstObjectByType<EquipmentPanel>();
            
        if (playerStats == null)
            playerStats = GetComponent<PlayerStats>();
            
        mainCamera = Camera.main;
    }

    private void Start(){
        if(playerStats == null){
            playerStats = PlayerManager.PlayerStats;
        }
    }
    
    private void Update()
    {
        // Update cooldowns
        var keysToUpdate = new List<int>(skillCooldowns.Keys);
        foreach (var key in keysToUpdate)
        {
            if (skillCooldowns[key] > 0)
            {
                skillCooldowns[key] -= Time.deltaTime;
                if (skillCooldowns[key] <= 0)
                {
                    skillCooldowns.Remove(key);
                }
            }
        }
    }
    
    public bool TryExecuteSkill(int slotIndex)
    {
        if (equipmentPanel == null) return false;
        
        // Check cooldown
        if (skillCooldowns.ContainsKey(slotIndex) && skillCooldowns[slotIndex] > 0)
        {
            Debug.Log($"Skill in slot {slotIndex} is on cooldown: {skillCooldowns[slotIndex]:F1}s remaining");
            return false;
        }
        
        // Get or cache the controller
        if (!cachedControllers.TryGetValue(slotIndex, out var controller) || controller == null || controller.skillGemInstance == null)
        {
            controller = equipmentPanel.GetActiveSkillController(slotIndex);
            if (controller == null || controller.skillGemInstance == null)
            {
                Debug.Log($"No skill gem equipped in slot {slotIndex}");
                return false;
            }
            cachedControllers[slotIndex] = controller;
        }
        
        var skillData = controller.skillGemInstance.gemDataTemplate as SkillGemData;
        if (skillData == null || skillData.skillPrefab == null)
        {
            Debug.LogError($"Skill gem in slot {slotIndex} has no prefab assigned!");
            return false;
        }
        
        // Check mana cost
        float manaCost = controller.CalculateFinalManaCost();
        if (playerStats.currentMana < manaCost)
        {
            Debug.Log($"Not enough mana. Required: {manaCost}, Current: {playerStats.currentMana}");
            return false;
        }
        
        // Get mouse position for targeting using new Input System
        Vector2 mouseScreenPos = Mouse.current.position.ReadValue();
        Vector3 mousePosition = mainCamera.ScreenToWorldPoint(new Vector3(mouseScreenPos.x, mouseScreenPos.y, mainCamera.nearClipPlane));
        mousePosition.z = 0;
        
        // Execute based on skill type
        switch (skillData.skillType)
        {
            case SkillType.Instant:
                ExecuteInstantSkill(controller, skillData, mousePosition);
                break;
                
            case SkillType.Projectile:
                ExecuteProjectileSkill(controller, skillData, mousePosition);
                break;
        }
        
        // Consume mana
        playerStats.SpendMana(manaCost);
        
        // Set cooldown (apply attack speed multiplier from gems and player stats)
        float cooldown = controller.CalculateFinalCooldown();
        float gemAttackSpeed = controller.CalculateFinalAttackSpeed();
        float playerAttackSpeed = playerStats?.GetCalculatedStat(StatType.AttackSpeed) ?? 1f;
        
        // Combine gem and player attack speed multipliers
        float totalAttackSpeed = gemAttackSpeed * playerAttackSpeed;
        
        if (totalAttackSpeed > 0)
        {
            cooldown /= totalAttackSpeed;
        }
        if (cooldown > 0)
        {
            skillCooldowns[slotIndex] = cooldown;
        }
        
        // Check for spell echo support gems
        CheckAndStartSpellEcho(slotIndex, controller, skillData, mousePosition);
        
        return true;
    }
    
    private void ExecuteInstantSkill(GemSocketController controller, SkillGemData skillData, Vector3 targetPosition)
    {
        Vector3 baseSpawnPosition = skillData.targetGroundPosition ? targetPosition : transform.position;
        
        // Get spell count from support gems (multi shot)
        int spellCount = controller.GetTotalProjectileCount();
        
        // Calculate spread radius for multiple instant spells
        float spreadRadius = 0f;
        if (spellCount > 1)
        {
            // Spread radius increases with spell count (1.5 units per extra spell)
            spreadRadius = (spellCount - 1) * 1.5f;
        }
        
        // Spawn multiple instant spells
        for (int i = 0; i < spellCount; i++)
        {
            Vector3 spawnPosition = baseSpawnPosition;
            
            // For multiple spells, spread them in a circle around the target
            if (spellCount > 1)
            {
                float angle = (360f / spellCount) * i * Mathf.Deg2Rad;
                Vector2 offset = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * spreadRadius;
                spawnPosition += (Vector3)offset;
            }
            
            GameObject skillObject = PoolManager.Instance.Spawn(skillData.skillPrefab, spawnPosition, Quaternion.identity);
            if (skillObject == null) continue;
            
            // Check if this is an InstantSpell component
            if (PoolManager.Instance.GetCachedComponent<InstantSpell>(skillObject, out var instantSpell))
            {
                // Calculate damage with player stats
                float gemDamage = controller.CalculateFinalDamage();
                float totalDamage = ApplyPlayerDamageModifiers(gemDamage, controller);
                float damageMultiplier = totalDamage / skillData.baseDamage;
                
                CollisionLayers layer = CollisionLayers.PlayerProjectile; // Assuming player skills
                
                // Use gem crit stats without player bonuses
                instantSpell.critChance = controller.CalculateFinalCritChance();
                instantSpell.critMultiplier = controller.CalculateFinalCritMultiplier();
                instantSpell.damageType = skillData.damageType;
                instantSpell.ailmentChance = skillData.ailmentChance;
                instantSpell.Initialize((Vector2)spawnPosition, damageMultiplier, layer);
            }
            else
            {
                // Fallback for other instant skill types
                ApplySkillEffects(skillObject, controller);
                
                // Handle area damage if supported
                if (controller.HasAreaDamage())
                {
                    float radius = controller.GetAreaRadius();
                    float gemDamage = controller.CalculateFinalDamage();
                    float totalDamage = ApplyPlayerDamageModifiers(gemDamage, controller);
                    ApplyAreaDamage(spawnPosition, totalDamage, radius);
                }
            }
        }
    }
    
    private void ExecuteProjectileSkill(GemSocketController controller, SkillGemData skillData, Vector3 targetPosition)
    {
        Vector2 baseDirection = (targetPosition - transform.position).normalized;
        float baseAngle = Mathf.Atan2(baseDirection.y, baseDirection.x) * Mathf.Rad2Deg;
        
        // Get projectile count and spread
        int projectileCount = controller.GetTotalProjectileCount();
        bool useParallel = controller.UseParallelProjectiles();
        
        // Spawn multiple projectiles
        for (int i = 0; i < projectileCount; i++)
        {
            Vector3 spawnPosition;
            Quaternion rotation;
            Vector2 direction;
            
            if (useParallel)
            {
                // Parallel projectiles - calculate lateral offset
                float lateralOffset = controller.GetProjectileLateralOffset();
                float totalWidth = (projectileCount - 1) * lateralOffset;
                float offsetAmount = -totalWidth / 2f + (i * lateralOffset);
                
                // Calculate perpendicular vector for offset
                Vector2 perpendicular = new Vector2(-baseDirection.y, baseDirection.x);
                spawnPosition = transform.position + (Vector3)(perpendicular * offsetAmount);
                
                // All parallel projectiles use the same angle and direction
                rotation = Quaternion.Euler(0, 0, baseAngle);
                direction = baseDirection;
            }
            else
            {
                // Angular spread (original behavior)
                float spreadAngle = controller.GetProjectileSpreadAngle();
                float angleStep = projectileCount > 1 ? spreadAngle : 0;
                float startAngle = baseAngle - (angleStep * (projectileCount - 1) / 2f);
                float currentAngle = startAngle + (angleStep * i);
                
                rotation = Quaternion.Euler(0, 0, currentAngle);
                float radians = currentAngle * Mathf.Deg2Rad;
                direction = new Vector2(Mathf.Cos(radians), Mathf.Sin(radians));
                spawnPosition = transform.position;
            }
            
            GameObject projectileObj = PoolManager.Instance.Spawn(skillData.skillPrefab, spawnPosition, rotation);
            if (projectileObj == null) continue;
            
            // Configure projectile
            if (PoolManager.Instance.GetCachedComponent<Projectile>(projectileObj, out var projectile))
            {
                // Set base stats with player modifiers
                float gemDamage = controller.CalculateFinalDamage();
                projectile.damage = ApplyPlayerDamageModifiers(gemDamage, controller);
                projectile.speed = skillData.projectileSpeed;
                projectile.lifetime = skillData.duration;
                projectile.critChance = controller.CalculateFinalCritChance();
                projectile.critMultiplier = controller.CalculateFinalCritMultiplier();
                projectile.damageType = skillData.damageType;
                projectile.ailmentChance = skillData.ailmentChance;

                // Pass gem data for status effect configuration
                projectile.skillGemData = skillData;
                projectile.supportGems = controller.GetCompatibleSupportGems();
                
                // Apply support gem effects
                if (controller.HasPierce())
                {
                    projectile.SetPiercing(true);
                }
                
                if (controller.HasChain())
                {
                    projectile.SetChaining(true, controller.GetChainCount());
                }
                
                if (controller.HasFork())
                {
                    projectile.SetFork(true, controller.GetForkCount(), controller.GetForkAngle());
                }
                
                if (controller.HasAreaDamage())
                {
                    projectile.SetAreaDamage(true, controller.GetAreaRadius());
                }
                
                // Initialize projectile with new parameters
                float totalDamage = ApplyPlayerDamageModifiers(controller.CalculateFinalDamage(), controller);
                float damageMultiplier = totalDamage / skillData.baseDamage;
                CollisionLayers layer = CollisionLayers.PlayerProjectile; // Assuming player skills
                projectile.Initialize((Vector2)spawnPosition, direction, damageMultiplier, layer, skillData.projectileSpeed, skillData.duration);
            }
        }
    }
    
    private void ApplySkillEffects(GameObject skillObject, GemSocketController controller)
    {
        // Apply damage multipliers and effects to the skill object
        // This would be customized based on your skill system
        
        // Example: Look for damage dealers on the skill object
        var damageDealers = skillObject.GetComponentsInChildren<IDamageDealer>();
        foreach (var dealer in damageDealers)
        {
            float gemDamage = controller.CalculateFinalDamage();
            float totalDamage = ApplyPlayerDamageModifiers(gemDamage, controller);
            dealer.SetDamage(totalDamage);
        }
    }
    
    private void ApplyAreaDamage(Vector3 center, float damage, float radius)
    {
        // Use custom collision system to find enemies in radius
        if (CollisionManager.Instance == null) return;
        
        var collidables = CollisionManager.Instance.GetCollidersInRadius(
            center, radius, CollisionLayers.Enemy);
            
        foreach (var collidable in collidables)
        {
            var target = collidable.GameObject;
            
            // Apply damage using the same system as projectiles
            if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
            {
                PlayerManager.DealDamageToPlayer(new DamageInfo(damage, DamageType.Physical));
            }
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
            {
                healthComponent.TakeDamage(damage);
            }
            else if (target.TryGetComponent<HealthComponent>(out var directHealth))
            {
                directHealth.TakeDamage(damage);
            }
            // Fallback to IDamageable for compatibility
            else if (target.TryGetComponent<IDamageable>(out var damageable))
            {
                damageable.TakeDamage(damage);
            }
        }
    }
    
    public void InvalidateCache(int slotIndex)
    {
        cachedControllers.Remove(slotIndex);
    }
    
    public void InvalidateAllCaches()
    {
        cachedControllers.Clear();
    }
    
    public bool IsSkillOnCooldown(int slotIndex)
    {
        return skillCooldowns.ContainsKey(slotIndex) && skillCooldowns[slotIndex] > 0;
    }
    
    public float GetRemainingCooldown(int slotIndex)
    {
        return skillCooldowns.TryGetValue(slotIndex, out float cooldown) ? cooldown : 0f;
    }
    
    // Player stat modifier methods
    private float ApplyPlayerDamageModifiers(float baseDamage, GemSocketController controller = null)
    {
        if (playerStats == null) return baseDamage;
        
        // Use the stat calculator system for damage calculations
        float playerDamageIncreased = playerStats.GetCalculatedStat(StatType.DamageIncreased);
        float damageMultiplier = playerStats.GetCalculatedStat(StatType.DamageMultiplier);
        
        // Get support gem increased damage (additive with player increased)
        float supportGemIncreased = controller?.GetTotalIncreasedDamage() ?? 0f;
        
        // Combine all "increased" modifiers additively
        float totalDamageIncreased = playerDamageIncreased + supportGemIncreased;
        
        // Formula: baseDamage * (1 + totalIncreased/100) * damageMultiplier
        // This follows the pattern: (flat) * (all increased damage) * (damage multiplier)
        
        // Apply damage increased (additive percentage)
        float damageWithIncreased = baseDamage * (1f + totalDamageIncreased / 100f);
        
        // Apply damage multiplier (multiplicative)
        float finalDamage = damageWithIncreased * damageMultiplier;
        
        return finalDamage;
    }
    
    private void CheckAndStartSpellEcho(int slotIndex, GemSocketController controller, SkillGemData skillData, Vector3 targetPosition)
    {
        // Only check for spell echo if the skill has the Spell tag
        if ((skillData.gemTags & GemTag.Spell) == 0)
            return;
            
        // Check for spell echo support gems
        foreach (var supportInstance in controller.GetCompatibleSupportGems())
        {
            if (supportInstance?.gemDataTemplate is SupportGemData supportGem && supportGem.addsSpellEcho)
            {
                // Get echo count with rarity scaling
                int echoes = supportInstance.GetSpellEchoCount();
                
                // Create spell echo data
                var echoData = new SpellEchoData
                {
                    slotIndex = slotIndex,
                    remainingEchoes = echoes,
                    targetPosition = targetPosition,
                    controller = controller,
                    skillData = skillData,
                    echoDelay = supportGem.echoDelay,
                    echoSpreadRadius = supportGem.echoSpreadRadius
                };
                
                // Start the echo coroutine
                var echoCoroutine = StartCoroutine(ProcessSpellEcho(echoData));
                activeSpellEchoes.Add(echoCoroutine);
                
                // Only apply the first spell echo gem found
                break;
            }
        }
    }
    
    private IEnumerator ProcessSpellEcho(SpellEchoData echoData)
    {
        while (echoData.remainingEchoes > 0)
        {
            // Wait for echo delay
            yield return new WaitForSeconds(echoData.echoDelay);
            
            // Check if the skill slot is still valid
            if (equipmentPanel == null || echoData.controller == null || echoData.skillData == null)
                break;
                
            // Calculate echo position with optional spread
            Vector3 echoPosition = echoData.targetPosition;
            if (echoData.echoSpreadRadius > 0)
            {
                Vector2 randomOffset = Random.insideUnitCircle * echoData.echoSpreadRadius;
                echoPosition += new Vector3(randomOffset.x, randomOffset.y, 0);
            }
            
            // Execute the echo cast (free - no mana cost)
            ExecuteEchoSkill(echoData.controller, echoData.skillData, echoPosition);
            
            echoData.remainingEchoes--;
        }
        
        // Remove from active echoes list
        activeSpellEchoes.RemoveAll(c => c == null);
    }
    
    private void ExecuteEchoSkill(GemSocketController controller, SkillGemData skillData, Vector3 targetPosition)
    {
        // Execute based on skill type (similar to normal execution but without mana/cooldown)
        switch (skillData.skillType)
        {
            case SkillType.Instant:
                ExecuteInstantSkill(controller, skillData, targetPosition);
                break;
                
            case SkillType.Projectile:
                ExecuteProjectileSkill(controller, skillData, targetPosition);
                break;
        }
        
        // Visual/audio feedback for echo cast
        if (ParticleEffectManager.Instance != null)
        {
            ParticleEffectManager.Instance.SpawnParticle(ParticleType.MagicAura, transform.position, 5);
        }
    }
}

// Interface for objects that can deal damage
public interface IDamageDealer
{
    void SetDamage(float damage);
}