%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d3d0b52bf8c3e684f96ec4333fba0f67, type: 3}
  m_Name: SpellEcho
  m_EditorClassIdentifier: 
  icon: {fileID: 0}
  gemName: Spell Echo
  description: Supported spells will automatically recast after a short delay
  rarity: 0
  commonStatMultiplier: 0.7
  uncommonStatMultiplier: 0.85
  rareStatMultiplier: 1
  epicStatMultiplier: 1.15
  uniqueStatMultiplier: 1.3
  useIntegerScaling: 1
  commonIntMultiplier: 0
  uncommonIntMultiplier: 0.5
  rareIntMultiplier: 1
  epicIntMultiplier: 1.5
  uniqueIntMultiplier: 2
  compatibleTags: 4
  damageIncreased: 0
  damageMore: 1
  cooldownMultiplier: 1
  manaCostMultiplier: 1.4
  attackSpeedMultiplier: 1
  addedCritChance: 0
  critMultiplierModifier: 1
  addsPierce: 0
  addsChain: 0
  addsAreaDamage: 0
  addsMultipleProjectiles: 0
  extraProjectiles: 2
  projectileSpreadAngle: 15
  chainCount: 2
  areaRadius: 3
  addsSpellEcho: 1
  echoDelay: 0.4
  echoCount: 1
  echoSpreadRadius: 0