using System;

/// <summary>
/// Types of modifiers that can be applied to support gems
/// </summary>
public enum SupportGemModifierType
{
    // Damage modifiers
    DamageMultiplier,           // +X% damage
    DamageFlat,                 // +X flat damage
    ElementalDamageMultiplier,  // +X% elemental damage
    
    // Speed modifiers
    CooldownReduction,          // -X% cooldown
    AttackSpeedMultiplier,      // +X% attack speed
    ProjectileSpeed,            // +X% projectile speed
    
    // Critical modifiers
    CriticalChance,             // +X% crit chance
    CriticalMultiplier,         // +X% crit damage
    
    // Resource modifiers
    ManaCostReduction,          // -X% mana cost
    ManaRegeneration,           // +X mana/sec while equipped
    
    // Projectile modifiers
    ExtraProjectiles,           // +X projectiles
    ProjectilePierce,           // +X pierce count
    ChainCount,                 // +X chain bounces
    
    // Area modifiers
    AreaOfEffect,               // +X% area radius
    AreaDamageMultiplier,       // +X% area damage
    
    // Special modifiers
    LifeLeech,                  // X% damage as life leech
    ManaLeech,                  // X% damage as mana leech
    ChanceToBleed,              // X% chance to cause bleed
    ChanceToFreeze,             // X% chance to freeze
    ChanceToPoison,             // X% chance to poison
    ChanceToShock,              // X% chance to shock
}

/// <summary>
/// Tier of modifier strength
/// </summary>
public enum ModifierTier
{
    Minor = 0,      // Common modifiers, low values
    Major = 1,      // Uncommon/Rare modifiers, medium values
    Epic = 2,       // Epic modifiers, high values
    Legendary = 3   // Unique modifiers, very high values
}

/// <summary>
/// Helper class for modifier type information
/// </summary>
public static class SupportGemModifierTypeExtensions
{
    public static bool IsPercentageModifier(this SupportGemModifierType type)
    {
        switch (type)
        {
            case SupportGemModifierType.DamageMultiplier:
            case SupportGemModifierType.ElementalDamageMultiplier:
            case SupportGemModifierType.CooldownReduction:
            case SupportGemModifierType.AttackSpeedMultiplier:
            case SupportGemModifierType.ProjectileSpeed:
            case SupportGemModifierType.CriticalChance:
            case SupportGemModifierType.CriticalMultiplier:
            case SupportGemModifierType.ManaCostReduction:
            case SupportGemModifierType.AreaOfEffect:
            case SupportGemModifierType.AreaDamageMultiplier:
            case SupportGemModifierType.LifeLeech:
            case SupportGemModifierType.ManaLeech:
            case SupportGemModifierType.ChanceToBleed:
            case SupportGemModifierType.ChanceToFreeze:
            case SupportGemModifierType.ChanceToPoison:
                return true;
            default:
                return false;
        }
    }
    
    public static string GetDisplayFormat(this SupportGemModifierType type, float value)
    {
        if (type.IsPercentageModifier())
        {
            // For reduction modifiers, show as negative
            if (type == SupportGemModifierType.CooldownReduction || 
                type == SupportGemModifierType.ManaCostReduction)
            {
                return $"{-value:F1}%";
            }
            return $"+{value:F1}%";
        }
        else
        {
            // Flat modifiers
            return $"+{value:F0}";
        }
    }
    
    public static string GetDisplayName(this SupportGemModifierType type)
    {
        switch (type)
        {
            case SupportGemModifierType.DamageMultiplier:
                return "Damage";
            case SupportGemModifierType.DamageFlat:
                return "Added Damage";
            case SupportGemModifierType.ElementalDamageMultiplier:
                return "Elemental Damage";
            case SupportGemModifierType.CooldownReduction:
                return "Cooldown Reduction";
            case SupportGemModifierType.AttackSpeedMultiplier:
                return "Attack Speed";
            case SupportGemModifierType.ProjectileSpeed:
                return "Projectile Speed";
            case SupportGemModifierType.CriticalChance:
                return "Critical Chance";
            case SupportGemModifierType.CriticalMultiplier:
                return "Critical Multiplier";
            case SupportGemModifierType.ManaCostReduction:
                return "Mana Cost";
            case SupportGemModifierType.ManaRegeneration:
                return "Mana Regeneration";
            case SupportGemModifierType.ExtraProjectiles:
                return "Additional Projectiles";
            case SupportGemModifierType.ProjectilePierce:
                return "Pierce";
            case SupportGemModifierType.ChainCount:
                return "Chain";
            case SupportGemModifierType.AreaOfEffect:
                return "Area of Effect";
            case SupportGemModifierType.AreaDamageMultiplier:
                return "Area Damage";
            case SupportGemModifierType.LifeLeech:
                return "Life Leech";
            case SupportGemModifierType.ManaLeech:
                return "Mana Leech";
            case SupportGemModifierType.ChanceToBleed:
                return "Bleed Chance";
            case SupportGemModifierType.ChanceToFreeze:
                return "Freeze Chance";
            case SupportGemModifierType.ChanceToPoison:
                return "Poison Chance";
            case SupportGemModifierType.ChanceToShock:
                return "Shock Chance";
            default:
                return type.ToString();
        }
    }
}