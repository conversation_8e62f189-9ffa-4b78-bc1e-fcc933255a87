%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b01c8757bc291840ad40332ed0d15f0, type: 3}
  m_Name: Rat-Shaman-Shoot
  m_EditorClassIdentifier: Assembly-CSharp::SpriteAnimation
  animationName: attack
  frameRate: 8
  loopMode: 0
  frames:
  - {fileID: 1973862800, guid: ee7b02bac07306a4bbd639dd9b70a406, type: 3}
  - {fileID: -937450816, guid: ee7b02bac07306a4bbd639dd9b70a406, type: 3}
  - {fileID: -51471561, guid: ee7b02bac07306a4bbd639dd9b70a406, type: 3}
  - {fileID: 317178148, guid: ee7b02bac07306a4bbd639dd9b70a406, type: 3}
  - {fileID: 981802721, guid: ee7b02bac07306a4bbd639dd9b70a406, type: 3}
  - {fileID: -80105612, guid: ee7b02bac07306a4bbd639dd9b70a406, type: 3}
  - {fileID: -27245791, guid: ee7b02bac07306a4bbd639dd9b70a406, type: 3}
  - {fileID: 1237716069, guid: ee7b02bac07306a4bbd639dd9b70a406, type: 3}
  speedMultiplier: 1
  frameEvents:
  - frameIndex: 5
    eventName: shoot
    parameter: ranged
