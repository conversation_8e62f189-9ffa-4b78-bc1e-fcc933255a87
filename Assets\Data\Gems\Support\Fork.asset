%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d3d0b52bf8c3e684f96ec4333fba0f67, type: 3}
  m_Name: Fork
  m_EditorClassIdentifier: 
  icon: {fileID: 21300000, guid: 2042a278ed7377a40acdd85443011a30, type: 3}
  backgroundIcon: {fileID: 0}
  foregroundIcon: {fileID: 0}
  gemName: Fork Support
  description: Projectiles split into multiple projectiles on impact
  rarity: 0
  useIntegerScaling: 1
  commonStatMultiplier: 0.7
  uncommonStatMultiplier: 0.85
  rareStatMultiplier: 1
  epicStatMultiplier: 1.15
  uniqueStatMultiplier: 1.3
  commonIntMultiplier: 2
  uncommonIntMultiplier: 2
  rareIntMultiplier: 3
  epicIntMultiplier: 3
  uniqueIntMultiplier: 4
  compatibleTags: 2
  damageIncreased: -30
  damageMore: 1
  cooldownMultiplier: 1
  manaCostMultiplier: 1.3
  attackSpeedMultiplier: 1
  addedCritChance: 0
  critMultiplierModifier: 1
  addsPierce: 0
  addsChain: 0
  addsAreaDamage: 0
  addsMultipleProjectiles: 0
  extraProjectiles: 2
  projectileSpreadAngle: 15
  useParallelProjectiles: 0
  projectileLateralOffset: 0.6
  chainCount: 2
  areaRadius: 3
  addsFork: 1
  forkCount: 2
  forkAngle: 30
  addsSpellEcho: 0
  echoDelay: 0.4
  echoCount: 1
  echoSpreadRadius: 0
