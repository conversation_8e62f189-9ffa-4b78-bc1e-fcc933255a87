using UnityEngine;
using Sirenix.OdinInspector;

[CreateAssetMenu(fileName = "New Support Gem", menuName = "2D Rogue/Items/Support Gem")]
public class SupportGemData : GemData
{
    [Title("Support Properties")]
    [InfoBox("'Increased' modifiers are additive, 'More' modifiers are multiplicative")]
    
    [Title("Rarity Scaling")]
    [InfoBox("Define how base stats scale with gem rarity. 1.0 = no scaling")]
    [FoldoutGroup("Rarity Scaling", false)]
    [SerializeField] private bool useIntegerScaling = false;
    [FoldoutGroup("Rarity Scaling")]
    [InfoBox("Float scaling for percentage values")]
    [HideIf("useIntegerScaling")]
    [SerializeField, Range(0.5f, 2f)] private float commonStatMultiplier = 0.7f;
    [FoldoutGroup("Rarity Scaling")]
    [HideIf("useIntegerScaling")]
    [SerializeField, Range(0.5f, 2f)] private float uncommonStatMultiplier = 0.85f;
    [FoldoutGroup("Rarity Scaling")]
    [HideIf("useIntegerScaling")]
    [SerializeField, Range(0.5f, 2f)] private float rareStatMultiplier = 1.0f;
    [FoldoutGroup("Rarity Scaling")]
    [HideIf("useIntegerScaling")]
    [SerializeField, Range(0.5f, 2f)] private float epicStatMultiplier = 1.15f;
    [FoldoutGroup("Rarity Scaling")]
    [HideIf("useIntegerScaling")]
    [SerializeField, Range(0.5f, 2f)] private float uniqueStatMultiplier = 1.3f;
    
    [FoldoutGroup("Rarity Scaling")]
    [InfoBox("Integer scaling for absolute values like projectiles/chains")]
    [ShowIf("useIntegerScaling")]
    [SerializeField, Range(0f, 10f)] private float commonIntMultiplier = 0.5f;
    [FoldoutGroup("Rarity Scaling")]
    [ShowIf("useIntegerScaling")]
    [SerializeField, Range(0f, 10f)] private float uncommonIntMultiplier = 0.75f;
    [FoldoutGroup("Rarity Scaling")]
    [ShowIf("useIntegerScaling")]
    [SerializeField, Range(0f, 10f)] private float rareIntMultiplier = 1.0f;
    [FoldoutGroup("Rarity Scaling")]
    [ShowIf("useIntegerScaling")]
    [SerializeField, Range(0f, 10f)] private float epicIntMultiplier = 1.5f;
    [FoldoutGroup("Rarity Scaling")]
    [ShowIf("useIntegerScaling")]
    [SerializeField, Range(0f, 10f)] private float uniqueIntMultiplier = 2.0f;
    
    [Title("Compatible Gem Tags")]
    [InfoBox("This support gem will only work with skill gems that have matching tags.")]
    [EnumToggleButtons]
    public GemTag compatibleTags = GemTag.Projectile;
    
    [Title("Damage Modifiers")]
    [Range(-50f, 200f)]
    [Tooltip("Additive damage increase percentage (stacks with other 'increased' modifiers)")]
    public float damageIncreased = 0f;
    
    [Range(0f, 2f)]
    [Tooltip("Multiplicative damage modifier (1.0 = no change, 1.3 = 30% more damage)")]
    public float damageMore = 1f;
    
    [Range(0f, 2f)]
    public float cooldownMultiplier = 1f;
    
    [Range(0f, 2f)]
    public float manaCostMultiplier = 1f;
    
    [Title("Combat Stats Modifiers")]
    [Range(0f, 2f)]
    [Tooltip("Attack speed multiplier for supported skills")]
    public float attackSpeedMultiplier = 1f;
    
    [Range(-50f, 50f)]
    [Tooltip("Added critical chance percentage")]
    public float addedCritChance = 0f;
    
    [Range(0f, 2f)]
    [Tooltip("Critical damage multiplier modifier")]
    public float critMultiplierModifier = 1f;
    
    [Title("Special Effects")]
    public bool addsPierce;
    public bool addsChain;
    public bool addsAreaDamage;
    public bool addsMultipleProjectiles;
    
    [ShowIf("addsMultipleProjectiles")]
    [Range(1, 10)]
    public int extraProjectiles = 2;
    
    [ShowIf("addsMultipleProjectiles")]
    [Range(5f, 45f)]
    [Tooltip("Angle spread between projectiles in degrees")]
    public float projectileSpreadAngle = 15f;
    
    [ShowIf("addsMultipleProjectiles")]
    [Tooltip("Use parallel projectiles instead of angular spread")]
    public bool useParallelProjectiles = false;
    
    [ShowIf("@addsMultipleProjectiles && useParallelProjectiles")]
    [Range(0.3f, 2f)]
    [Tooltip("Lateral spacing between parallel projectiles")]
    public float projectileLateralOffset = 0.6f;
    
    [ShowIf("addsChain")]
    [Range(1, 5)]
    public int chainCount = 2;
    
    [ShowIf("addsAreaDamage")]
    [Range(1f, 10f)]
    public float areaRadius = 3f;
    
    [Title("Fork Support")]
    [InfoBox("Projectiles split into multiple projectiles on impact")]
    public bool addsFork;

    [ShowIf("addsFork")]
    [Range(2, 5)]
    [Tooltip("Number of projectiles created when forking")]
    public int forkCount = 2;

    [Title("Status Effect Modifiers")]
    [InfoBox("These modifiers enhance status effects applied by supported skills")]

    [Range(0f, 2f)]
    [Tooltip("Multiplier for ignite damage (1.0 = no change, 1.5 = 50% more damage)")]
    public float igniteEffectivenessMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for ignite duration (1.0 = no change, 1.3 = 30% longer)")]
    public float igniteDurationMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for freeze effectiveness (1.0 = no change, 1.2 = 20% stronger slow)")]
    public float freezeEffectivenessMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for freeze duration (1.0 = no change, 1.5 = 50% longer)")]
    public float freezeDurationMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for bleed damage (1.0 = no change, 1.4 = 40% more damage)")]
    public float bleedEffectivenessMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for bleed duration (1.0 = no change, 1.3 = 30% longer)")]
    public float bleedDurationMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for shock chain damage (1.0 = no change, 1.5 = 50% more damage)")]
    public float shockEffectivenessMultiplier = 1f;

    [Range(0f, 2f)]
    [Tooltip("Multiplier for shock chain range (1.0 = no change, 1.3 = 30% more range)")]
    public float shockRangeMultiplier = 1f;
    
    [ShowIf("addsFork")]
    [Range(10f, 60f)]
    [Tooltip("Angle spread between forked projectiles in degrees")]
    public float forkAngle = 30f;
    
    [Title("Spell Echo")]
    [InfoBox("Automatically recasts supported spells after a delay")]
    public bool addsSpellEcho;
    
    [ShowIf("addsSpellEcho")]
    [Range(0.1f, 2f)]
    [Tooltip("Delay in seconds before the spell echoes")]
    public float echoDelay = 0.4f;
    
    [ShowIf("addsSpellEcho")]
    [Range(1, 5)]
    [Tooltip("Number of times the spell will echo")]
    public int echoCount = 1;
    
    [ShowIf("addsSpellEcho")]
    [Range(0f, 3f)]
    [Tooltip("Random position offset for echo casts (0 = exact same position)")]
    public float echoSpreadRadius = 0f;
    
    public override string GetTooltipText()
    {
        var tooltip = $"<color=#{ColorUtility.ToHtmlStringRGB(GetRarityColor())}>{gemName}</color>\n" +
             $"{rarity} Support Gem\n";
             
        // Add compatible tags display
        if (compatibleTags != GemTag.None)
        {
            var tagNames = new System.Collections.Generic.List<string>();
            if ((compatibleTags & GemTag.Melee) != 0) tagNames.Add(GemTag.Melee.GetColoredDisplayName());
            if ((compatibleTags & GemTag.Projectile) != 0) tagNames.Add(GemTag.Projectile.GetColoredDisplayName());
            if ((compatibleTags & GemTag.Spell) != 0) tagNames.Add(GemTag.Spell.GetColoredDisplayName());
            tooltip += $"Compatible with: {string.Join(", ", tagNames)}\n";
        }
        
        tooltip += $"\n{description}\n\n";
        
        if (damageIncreased != 0f)
        tooltip += $"<color=#90EE90>{damageIncreased:+0;-0}% increased Damage</color>\n";
        
        if (damageMore != 1f)
        tooltip += $"<color=#FFD700>{(damageMore - 1f) * 100:+0;-0}% more Damage</color>\n";
        
        if (cooldownMultiplier != 1f)
        tooltip += $"Cooldown: {(cooldownMultiplier - 1f) * 100:+0;-0}%\n";
        
        if (manaCostMultiplier != 1f)
        tooltip += $"Mana Cost: {(manaCostMultiplier - 1f) * 100:+0;-0}%\n";
        
        if (attackSpeedMultiplier != 1f)
        tooltip += $"Attack Speed: {(attackSpeedMultiplier - 1f) * 100:+0;-0}%\n";
        
        if (addedCritChance != 0f)
        tooltip += $"Critical Chance: {addedCritChance:+0;-0}%\n";
        
        if (critMultiplierModifier != 1f)
        tooltip += $"Critical Multiplier: {(critMultiplierModifier - 1f) * 100:+0;-0}%\n";
        
        if (addsPierce)
        tooltip += "Adds Piercing\n";
        
        if (addsChain)
        tooltip += $"Chains {chainCount} times\n";
        
        if (addsFork)
        tooltip += $"Forks into {forkCount} projectiles (Spread: {forkAngle}°)\n";
        
        if (addsAreaDamage)
        tooltip += $"Area Damage (Radius: {areaRadius})\n";
        
        if (addsMultipleProjectiles)
        {
            if (useParallelProjectiles)
                tooltip += $"+{extraProjectiles} Projectiles (Parallel, {projectileLateralOffset:F1}u spacing)\n";
            else
                tooltip += $"+{extraProjectiles} Projectiles (Spread: {projectileSpreadAngle}°)\n";
        }
        
        if (addsSpellEcho)
        {
            tooltip += $"Spell Echo: Recasts {echoCount} time{(echoCount > 1 ? "s" : "")} after {echoDelay}s\n";
            if (echoSpreadRadius > 0)
                tooltip += $"Echo Spread Radius: {echoSpreadRadius}\n";
        }

        // Add status effect modifiers if any are present
        bool hasStatusEffectModifiers = igniteEffectivenessMultiplier != 1f || igniteDurationMultiplier != 1f ||
                                       freezeEffectivenessMultiplier != 1f || freezeDurationMultiplier != 1f ||
                                       bleedEffectivenessMultiplier != 1f || bleedDurationMultiplier != 1f ||
                                       shockEffectivenessMultiplier != 1f || shockRangeMultiplier != 1f;

        if (hasStatusEffectModifiers)
        {
            tooltip += "\n<color=#FFD700>Status Effect Modifiers:</color>\n";

            // Ignite modifiers
            if (igniteEffectivenessMultiplier != 1f)
                tooltip += $"<color=#FF6B35>• Ignite Damage:</color> {(igniteEffectivenessMultiplier - 1f) * 100:+0;-0}%\n";
            if (igniteDurationMultiplier != 1f)
                tooltip += $"<color=#FF6B35>• Ignite Duration:</color> {(igniteDurationMultiplier - 1f) * 100:+0;-0}%\n";

            // Freeze modifiers
            if (freezeEffectivenessMultiplier != 1f)
                tooltip += $"<color=#4FC3F7>• Freeze Effectiveness:</color> {(freezeEffectivenessMultiplier - 1f) * 100:+0;-0}%\n";
            if (freezeDurationMultiplier != 1f)
                tooltip += $"<color=#4FC3F7>• Freeze Duration:</color> {(freezeDurationMultiplier - 1f) * 100:+0;-0}%\n";

            // Bleed modifiers
            if (bleedEffectivenessMultiplier != 1f)
                tooltip += $"<color=#8B0000>• Bleed Damage:</color> {(bleedEffectivenessMultiplier - 1f) * 100:+0;-0}%\n";
            if (bleedDurationMultiplier != 1f)
                tooltip += $"<color=#8B0000>• Bleed Duration:</color> {(bleedDurationMultiplier - 1f) * 100:+0;-0}%\n";

            // Shock modifiers
            if (shockEffectivenessMultiplier != 1f)
                tooltip += $"<color=#FFE082>• Shock Damage:</color> {(shockEffectivenessMultiplier - 1f) * 100:+0;-0}%\n";
            if (shockRangeMultiplier != 1f)
                tooltip += $"<color=#FFE082>• Shock Range:</color> {(shockRangeMultiplier - 1f) * 100:+0;-0}%\n";
        }

        return tooltip;
    }

    /// <summary>
    /// Apply this support gem's status effect modifiers to a base configuration
    /// </summary>
    /// <param name="baseConfig">Base status effect configuration to modify</param>
    /// <returns>Modified configuration with support gem bonuses applied</returns>
    public StatusEffectHelper.StatusEffectConfig ApplyStatusEffectModifiers(StatusEffectHelper.StatusEffectConfig baseConfig)
    {
        var modifiedConfig = baseConfig;

        // Apply ignite modifiers
        modifiedConfig.ignitePercent *= igniteEffectivenessMultiplier;
        modifiedConfig.igniteDuration *= igniteDurationMultiplier;

        // Apply freeze modifiers
        modifiedConfig.freezeSlowAmount = Mathf.Min(1f, modifiedConfig.freezeSlowAmount * freezeEffectivenessMultiplier);
        modifiedConfig.freezeDuration *= freezeDurationMultiplier;

        // Apply bleed modifiers
        modifiedConfig.bleedPercent *= bleedEffectivenessMultiplier;
        modifiedConfig.bleedDuration *= bleedDurationMultiplier;

        // Apply shock modifiers
        modifiedConfig.shockChainDamage *= shockEffectivenessMultiplier;
        modifiedConfig.shockChainRange *= shockRangeMultiplier;

        return modifiedConfig;
    }

    /// <summary>
    /// Get the rarity multiplier for scaling base stats
    /// </summary>
    public float GetRarityMultiplier(GemRarity gemRarity)
    {
        if (useIntegerScaling)
        {
            return gemRarity switch
            {
                GemRarity.Common => commonIntMultiplier,
                GemRarity.Uncommon => uncommonIntMultiplier,
                GemRarity.Rare => rareIntMultiplier,
                GemRarity.Epic => epicIntMultiplier,
                GemRarity.Unique => uniqueIntMultiplier,
                _ => 1f
            };
        }
        else
        {
            return gemRarity switch
            {
                GemRarity.Common => commonStatMultiplier,
                GemRarity.Uncommon => uncommonStatMultiplier,
                GemRarity.Rare => rareStatMultiplier,
                GemRarity.Epic => epicStatMultiplier,
                GemRarity.Unique => uniqueStatMultiplier,
                _ => 1f
            };
        }
    }
    
    /// <summary>
    /// Check if this support gem uses integer scaling
    /// </summary>
    public bool UsesIntegerScaling => useIntegerScaling;
}