%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: UniversalEffect
  m_Shader: {fileID: 4800000, guid: b1c292f58c533eb4da3532e793c118f4, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _SPRITESHEETFIX_ON
  m_InvalidKeywords:
  - _ENABLEBURN_ON
  - _ENABLEFROZEN_ON
  - _ENABLETEXTURELAYER1_ON
  - _PIXELPERFECTSPACE_ON
  - _PIXELPERFECTUV_ON
  - _SHADERFADING_NONE
  - _SHADERSPACE_UV
  - _TEXTURELAYER1SHEETTOGGLE_ON
  - _TEXTURELAYER2SHEETTOGGLE_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AddColorMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AddHueMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AlphaTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CustomFadeFadeMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FadingMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _InnerOutlineTintTexture:
        m_Texture: {fileID: 2800000, guid: 5af9a397a8643994c829e696a24a7845, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LightningTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: dc6c3ccd4bfbcc640a3be892349c5d77, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetalMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OuterOutlineTintTexture:
        m_Texture: {fileID: 2800000, guid: 5af9a397a8643994c829e696a24a7845, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _PixelOutlineTintTexture:
        m_Texture: {fileID: 2800000, guid: 5af9a397a8643994c829e696a24a7845, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RecolorRGBTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RecolorRGBYCPTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShineMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SineGlowMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _StrongTintMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureLayer1Texture:
        m_Texture: {fileID: 2800000, guid: 9685c3a024e22a946a7e8c2ce3542300, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureLayer2Texture:
        m_Texture: {fileID: 2800000, guid: 9685c3a024e22a946a7e8c2ce3542300, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UVDistortMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UberNoiseTexture:
        m_Texture: {fileID: 2800000, guid: b8d18cd117976254d94a812a0bfc336e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - PixelSnap: 0
    - _AddColorContrast: 0.5
    - _AddColorContrastToggle: 0
    - _AddColorFade: 1
    - _AddColorMaskToggle: 0
    - _AddHueBrightness: 2
    - _AddHueContrast: 0.5
    - _AddHueFade: 1
    - _AddHueMaskToggle: 0
    - _AddHueSaturation: 1
    - _AddHueSpeed: 1
    - _AlphaCutoff: 0.5
    - _AlphaTintFade: 1
    - _AlphaTintMinAlpha: 0.02
    - _BakedMaterial: 0
    - _BlackTintFade: 1
    - _BlackTintPower: 4
    - _Brightness: 1
    - _BurnEdgeNoiseFactor: 0.5
    - _BurnFade: 0
    - _BurnInsideContrast: 2
    - _BurnInsideNoiseFactor: 0.2
    - _BurnRadius: 5
    - _BurnSwirlFactor: 1
    - _BurnWidth: 0.1
    - _CamouflageAnimationToggle: 0
    - _CamouflageContrast: 1
    - _CamouflageDensityA: 0.4
    - _CamouflageDensityB: 0.4
    - _CamouflageFade: 1
    - _CamouflageSmoothnessA: 0.2
    - _CamouflageSmoothnessB: 0.2
    - _CheckerboardDarken: 0.5
    - _CheckerboardTiling: 1
    - _ColorReplaceContrast: 1
    - _ColorReplaceFade: 1
    - _ColorReplaceRange: 0.05
    - _ColorReplaceSmoothness: 0.1
    - _Contrast: 1
    - _CustomFadeAlpha: 1
    - _CustomFadeNoiseFactor: 0
    - _CustomFadeSmoothness: 2
    - _DirectionalAlphaFadeFade: 0
    - _DirectionalAlphaFadeInvert: 0
    - _DirectionalAlphaFadeNoiseFactor: 0.2
    - _DirectionalAlphaFadeRotation: 0
    - _DirectionalAlphaFadeWidth: 0.2
    - _DirectionalDistortionFade: 0
    - _DirectionalDistortionInvert: 0
    - _DirectionalDistortionNoiseFactor: 0.2
    - _DirectionalDistortionRandomDirection: 0.1
    - _DirectionalDistortionRotation: 0
    - _DirectionalDistortionWidth: 0.5
    - _DirectionalGlowFadeFade: 0
    - _DirectionalGlowFadeInvert: 0
    - _DirectionalGlowFadeNoiseFactor: 0.2
    - _DirectionalGlowFadeRotation: 0
    - _DirectionalGlowFadeWidth: 0.1
    - _EnableAddColor: 0
    - _EnableAddHue: 0
    - _EnableAlphaTint: 0
    - _EnableBlackTint: 0
    - _EnableBrightness: 0
    - _EnableBurn: 1.1
    - _EnableCamouflage: 0
    - _EnableCheckerboard: 0
    - _EnableColorReplace: 0
    - _EnableContrast: 0
    - _EnableCustomFade: 0
    - _EnableDirectionalAlphaFade: 0
    - _EnableDirectionalDistortion: 0
    - _EnableDirectionalGlowFade: 0
    - _EnableEnchanted: 0
    - _EnableExternalAlpha: 0
    - _EnableFlame: 0
    - _EnableFrost: 0
    - _EnableFrozen: 1.1
    - _EnableFullAlphaDissolve: 0
    - _EnableFullDistortion: 0
    - _EnableFullGlowDissolve: 0
    - _EnableGaussianBlur: 0
    - _EnableGlitch: 0
    - _EnableHalftone: 0
    - _EnableHologram: 0
    - _EnableHue: 0
    - _EnableIgnite: 0
    - _EnableInkSpread: 0
    - _EnableInnerOutline: 0
    - _EnableLightning: 0
    - _EnableMetal: 0
    - _EnableNegative: 0
    - _EnableOuterOutline: 0
    - _EnablePingPongGlow: 0
    - _EnablePixelOutline: 0
    - _EnablePixelate: 0
    - _EnablePoison: 0
    - _EnableRainbow: 0
    - _EnableRecolorRGB: 0
    - _EnableRecolorRGBYCP: 0
    - _EnableSaturation: 0
    - _EnableScreenTiling: 0
    - _EnableShadow: 0
    - _EnableSharpen: 0
    - _EnableShiftHue: 0
    - _EnableShifting: 0
    - _EnableShine: 0
    - _EnableSineGlow: 0
    - _EnableSineMove: 0
    - _EnableSineRotate: 0
    - _EnableSineScale: 0
    - _EnableSmoke: 0
    - _EnableSmoothPixelArt: 0
    - _EnableSourceAlphaDissolve: 0
    - _EnableSourceGlowDissolve: 0
    - _EnableSplitToning: 0
    - _EnableSqueeze: 0
    - _EnableSquish: 0
    - _EnableStrongTint: 0
    - _EnableTextureLayer1: 1
    - _EnableTextureLayer2: 0
    - _EnableUVDistort: 0
    - _EnableUVRotate: 0
    - _EnableUVScale: 0
    - _EnableUVScroll: 0
    - _EnableVibrate: 0
    - _EnableWiggle: 0
    - _EnableWind: 0
    - _EnableWorldTiling: 0
    - _EnchantedBrightness: 1
    - _EnchantedContrast: 0.5
    - _EnchantedFade: 1
    - _EnchantedLerpToggle: 0
    - _EnchantedRainbowDensity: 0.5
    - _EnchantedRainbowSaturation: 0.8
    - _EnchantedRainbowSpeed: 0.5
    - _EnchantedRainbowToggle: 0
    - _EnchantedReduce: 0
    - _FadingFade: 1
    - _FadingNoiseFactor: 0.2
    - _FadingWidth: 0.3
    - _FlameBrightness: 10
    - _FlameNoiseFactor: 2.5
    - _FlameNoiseHeightFactor: 1.5
    - _FlameRadius: 0.2
    - _FlameSmooth: 2
    - _FrostAmount: 0
    - _FrostContrast: 1.5
    - _FrostHighlightDensity: 0.5
    - _FrostNoiseScale: 5
    - _FrostRimPower: 2
    - _FrozenContrast: 2
    - _FrozenFade: 0
    - _FrozenHighlightContrast: 2
    - _FrozenHighlightDensity: 1
    - _FrozenSnowContrast: 1
    - _FrozenSnowDensity: 0.22
    - _FullAlphaDissolveFade: 0.5
    - _FullAlphaDissolveWidth: 0.5
    - _FullDistortionFade: 1
    - _FullGlowDissolveFade: 0.5
    - _FullGlowDissolveWidth: 0.5
    - _GaussianBlurFade: 1
    - _GaussianBlurOffset: 0.5
    - _GlitchBrightness: 4
    - _GlitchFade: 1
    - _GlitchHueSpeed: 1
    - _GlitchMaskMin: 0.4
    - _HalftoneFade: 1
    - _HalftoneFadeWidth: 1.5
    - _HalftoneInvert: 0
    - _HalftoneTiling: 4
    - _HologramContrast: 1
    - _HologramDistortionDensity: 0.5
    - _HologramDistortionOffset: 0.5
    - _HologramDistortionScale: 10
    - _HologramDistortionSpeed: 2
    - _HologramFade: 1
    - _HologramLineFrequency: 500
    - _HologramLineGap: 3
    - _HologramLineSpeed: 0.01
    - _HologramMinAlpha: 0.2
    - _Hue: 0
    - _IceRoughness: 0.5
    - _IgniteAmount: 0
    - _IgniteDissolveAmount: 0.1
    - _IgniteNoiseScale: 5
    - _IgniteNoiseSpeed: 2
    - _IgniteOutlineWidth: 0.02
    - _IgniteWaveFrequency: 10
    - _IgniteWaveSpeed: 3
    - _InkSpreadContrast: 2
    - _InkSpreadDistance: 3
    - _InkSpreadFade: 1
    - _InkSpreadNoiseFactor: 0.5
    - _InkSpreadWidth: 0.2
    - _InnerOutlineDistortionToggle: 0
    - _InnerOutlineFade: 1
    - _InnerOutlineOutlineOnlyToggle: 0
    - _InnerOutlineTextureToggle: 0
    - _InnerOutlineWidth: 0.02
    - _LightningAmount: 0
    - _LightningFrequency: 10
    - _LightningIntensity: 2
    - _LightningScale: 1
    - _LightningSpeed: 5
    - _MaskBrightness: 1
    - _MetalContrast: 2
    - _MetalFade: 1
    - _MetalHighlightContrast: 2
    - _MetalHighlightDensity: 1
    - _MetalMaskToggle: 0
    - _NegativeFade: 1
    - _NormalIntensity: 1
    - _OuterOutlineDistortionToggle: 0
    - _OuterOutlineFade: 1
    - _OuterOutlineOutlineOnlyToggle: 0
    - _OuterOutlineTextureToggle: 0
    - _OuterOutlineWidth: 0.04
    - _PingPongGlowContrast: 1
    - _PingPongGlowFade: 1
    - _PingPongGlowFrequency: 3
    - _PixelOutlineAlphaLimit: 0.5
    - _PixelOutlineFade: 1
    - _PixelOutlineOutlineOnlyToggle: 0
    - _PixelOutlineTextureToggle: 0
    - _PixelOutlineWidth: 1
    - _PixelPerfectSpace: 1
    - _PixelPerfectUV: 1
    - _PixelateFade: 1
    - _PixelatePixelDensity: 16
    - _PixelatePixelsPerUnit: 100
    - _PixelsPerUnit: 32
    - _PoisonDensity: 3
    - _PoisonFade: 0
    - _PoisonNoiseBrightness: 2
    - _PoisonRecolorFactor: 0
    - _PoisonShiftSpeed: 0.2
    - _RainbowBrightness: 2
    - _RainbowContrast: 1
    - _RainbowDensity: 0.5
    - _RainbowFade: 1
    - _RainbowNoiseFactor: 0.2
    - _RainbowSaturation: 1
    - _RainbowSpeed: 1
    - _RecolorRGBFade: 1
    - _RecolorRGBTextureToggle: 0
    - _RecolorRGBYCPFade: 1
    - _RecolorRGBYCPTextureToggle: 0
    - _RectHeight: 100
    - _RectWidth: 100
    - _Saturation: 1
    - _ScreenTilingPixelsPerUnit: 100
    - _ScreenWidthUnits: 10
    - _ShaderFading: 0
    - _ShaderSpace: 0
    - _ShadowFade: 1
    - _SharpenFactor: 4
    - _SharpenFade: 1
    - _SharpenOffset: 2
    - _ShiftHueSpeed: 0.5
    - _ShiftingBrightness: 1
    - _ShiftingContrast: 0.5
    - _ShiftingDensity: 1.5
    - _ShiftingFade: 1
    - _ShiftingRainbowToggle: 0
    - _ShiftingSaturation: 0.8
    - _ShiftingSpeed: 0.5
    - _ShineContrast: 2
    - _ShineFade: 1
    - _ShineFrequency: 0.3
    - _ShineMaskToggle: 0
    - _ShineRotation: 30
    - _ShineSaturation: 0.5
    - _ShineSmooth: 1
    - _ShineSpeed: 5
    - _ShineWidth: 0.1
    - _SineGlowContrast: 1
    - _SineGlowFade: 1
    - _SineGlowFrequency: 4
    - _SineGlowMaskToggle: 0
    - _SineGlowMax: 1
    - _SineGlowMin: 0
    - _SineMoveFade: 1
    - _SineRotateAngle: 15
    - _SineRotateFade: 1
    - _SineRotateFrequency: 1
    - _SineScaleFrequency: 2
    - _SmokeAlpha: 1
    - _SmokeDarkEdge: 1
    - _SmokeNoiseFactor: 0.4
    - _SmokeNoiseScale: 0.5
    - _SmokeSmoothness: 1
    - _SmokeVertexSeed: 0
    - _SourceAlphaDissolveFade: 1
    - _SourceAlphaDissolveInvert: 0
    - _SourceAlphaDissolveNoiseFactor: 0.2
    - _SourceAlphaDissolveWidth: 0.2
    - _SourceGlowDissolveFade: 1
    - _SourceGlowDissolveInvert: 0
    - _SourceGlowDissolveNoiseFactor: 0.2
    - _SourceGlowDissolveWidth: 0.1
    - _SplitToningBalance: 1
    - _SplitToningContrast: 1
    - _SplitToningFade: 1
    - _SplitToningShift: 0
    - _SpriteSheetFix: 1
    - _SqueezeFade: 1
    - _SqueezePower: 1
    - _SquishFade: 1
    - _SquishFlip: 0
    - _SquishSquish: 0.1
    - _SquishStretch: 0.1
    - _StrongTintContrast: 0
    - _StrongTintContrastToggle: 0
    - _StrongTintFade: 1
    - _StrongTintMaskToggle: 0
    - _TextureLayer1Columns: 3
    - _TextureLayer1Contrast: 1
    - _TextureLayer1ContrastToggle: 0
    - _TextureLayer1EdgeClip: 0.005
    - _TextureLayer1Fade: 1
    - _TextureLayer1Rows: 3
    - _TextureLayer1ScrollToggle: 0
    - _TextureLayer1SheetToggle: 1
    - _TextureLayer1Speed: 20
    - _TextureLayer1StartFrame: 0
    - _TextureLayer2Columns: 3
    - _TextureLayer2Contrast: 1
    - _TextureLayer2ContrastToggle: 0
    - _TextureLayer2EdgeClip: 0.005
    - _TextureLayer2Fade: 1
    - _TextureLayer2Rows: 3
    - _TextureLayer2ScrollToggle: 0
    - _TextureLayer2SheetToggle: 1
    - _TextureLayer2Speed: 20
    - _TextureLayer2StartFrame: 0
    - _TilingFix: 0
    - _TimeFPS: 5
    - _TimeFrequency: 2
    - _TimeRange: 0.5
    - _TimeSpeed: 1
    - _TimeValue: 0
    - _ToggleCustomTime: 0
    - _ToggleTimeFPS: 0
    - _ToggleTimeFrequency: 0
    - _ToggleTimeSpeed: 0
    - _ToggleUnscaledTime: 0
    - _UVDistortFade: 1
    - _UVDistortMaskToggle: 0
    - _UVRotateSpeed: 1
    - _VertexTintFirst: 0
    - _VibrateFade: 1
    - _VibrateFrequency: 100
    - _VibrateOffset: 0.04
    - _VibrateRotation: 4
    - _WiggleFade: 1
    - _WiggleFixedGroundToggle: 0
    - _WiggleFrequency: 2
    - _WiggleOffset: 0.02
    - _WiggleSpeed: 2
    - _WindFlip: 0
    - _WindHighQualityNoise: 0
    - _WindIsParallax: 0
    - _WindLocalWind: 0
    - _WindMaxIntensity: 0.4
    - _WindMaxRotation: 2
    - _WindMinIntensity: -0.4
    - _WindNoiseScale: 0.1
    - _WindNoiseSpeed: 1
    - _WindRotation: 0
    - _WindRotationWindFactor: 1
    - _WindSquishFactor: 0.3
    - _WindSquishWindFactor: 0
    - _WindXPosition: 0
    - _WorldTilingPixelsPerUnit: 100
    - _ZWrite: 0
    m_Colors:
    - _AddColorColor: {r: 2.996078, g: 0, b: 0, a: 0}
    - _AlphaTintColor: {r: 95.87451, g: 5.019608, b: 95.87451, a: 0}
    - _BlackTintColor: {r: 0, g: 0, b: 1, a: 0}
    - _BurnEdgeColor: {r: 11.98431, g: 1.129412, b: 0.1254902, a: 0}
    - _BurnEdgeNoiseScale: {r: 0.3, g: 0.3, b: 0, a: 0}
    - _BurnInsideColor: {r: 0.75, g: 0.5625, b: 0.525, a: 0}
    - _BurnInsideNoiseColor: {r: 3084.047, g: 257.0039, b: 0, a: 0}
    - _BurnInsideNoiseScale: {r: 0.5, g: 0.5, b: 0, a: 0}
    - _BurnPosition: {r: 0, g: 5, b: 0, a: 0}
    - _BurnSwirlNoiseScale: {r: 0.1, g: 0.1, b: 0, a: 0}
    - _CamouflageBaseColor: {r: 0.7450981, g: 0.7254902, b: 0.5686275, a: 0}
    - _CamouflageColorA: {r: 0.627451, g: 0.5882353, b: 0.4313726, a: 0}
    - _CamouflageColorB: {r: 0.4705882, g: 0.4313726, b: 0.3137255, a: 0}
    - _CamouflageDistortionIntensity: {r: 0.1, g: 0.1, b: 0, a: 0}
    - _CamouflageDistortionScale: {r: 0.5, g: 0.5, b: 0, a: 0}
    - _CamouflageDistortionSpeed: {r: 0.1, g: 0.1, b: 0, a: 0}
    - _CamouflageNoiseScaleA: {r: 0.25, g: 0.25, b: 0, a: 0}
    - _CamouflageNoiseScaleB: {r: 0.25, g: 0.25, b: 0, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorReplaceFromColor: {r: 0, g: 0, b: 0, a: 0}
    - _ColorReplaceToColor: {r: 0, g: 0, b: 0.2, a: 0}
    - _CustomFadeNoiseScale: {r: 1, g: 1, b: 0, a: 0}
    - _DirectionalAlphaFadeNoiseScale: {r: 0.3, g: 0.3, b: 0, a: 0}
    - _DirectionalDistortionDistortion: {r: 0, g: 0.1, b: 0, a: 0}
    - _DirectionalDistortionDistortionScale: {r: 1, g: 1, b: 0, a: 0}
    - _DirectionalDistortionNoiseScale: {r: 0.4, g: 0.4, b: 0, a: 0}
    - _DirectionalGlowFadeEdgeColor: {r: 11.98431, g: 0.6901961, b: 0.6901961, a: 0}
    - _DirectionalGlowFadeNoiseScale: {r: 0.4, g: 0.4, b: 0, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _EnchantedHighColor: {r: 0, g: 0.7098798, b: 4.237095, a: 0}
    - _EnchantedLowColor: {r: 2.996078, g: 0, b: 0, a: 0}
    - _EnchantedScale: {r: 0.1, g: 0.1, b: 0, a: 0}
    - _EnchantedSpeed: {r: 0, g: 1, b: 0, a: 0}
    - _FadingNoiseScale: {r: 0.2, g: 0.2, b: 0, a: 0}
    - _FadingPosition: {r: 0, g: 0, b: 0, a: 0}
    - _FlameNoiseScale: {r: 1.2, g: 0.8, b: 0, a: 0}
    - _FlameSpeed: {r: 0, g: -0.5, b: 0, a: 0}
    - _Flip: {r: 1, g: 1, b: 1, a: 1}
    - _FrostHighlightColor: {r: 2, g: 2.5, b: 3, a: 1}
    - _FrostRimColor: {r: 1.5, g: 2, b: 2.5, a: 1}
    - _FrostTint: {r: 0.7, g: 0.9, b: 1, a: 1}
    - _FrozenHighlightColor: {r: 1.797647, g: 4.604501, b: 5.992157, a: 1}
    - _FrozenHighlightDistortion: {r: 0.5, g: 0.5, b: 0, a: 0}
    - _FrozenHighlightDistortionScale: {r: 0.2, g: 0.2, b: 0, a: 0}
    - _FrozenHighlightDistortionSpeed: {r: -0.05, g: -0.05, b: 0, a: 0}
    - _FrozenHighlightScale: {r: 0.2, g: 0.2, b: 0, a: 0}
    - _FrozenHighlightSpeed: {r: 0.1, g: 0.1, b: 0, a: 0}
    - _FrozenSnowColor: {r: 1.123529, g: 1.373203, b: 1.498039, a: 0}
    - _FrozenSnowScale: {r: 0.1, g: 0.1, b: 0, a: 0}
    - _FrozenTint: {r: 1.819608, g: 4.611765, b: 5.992157, a: 0}
    - _FullAlphaDissolveNoiseScale: {r: 0.1, g: 0.1, b: 0, a: 0}
    - _FullDistortionDistortion: {r: 0.2, g: 0.2, b: 0, a: 0}
    - _FullDistortionNoiseScale: {r: 0.5, g: 0.5, b: 0, a: 0}
    - _FullGlowDissolveEdgeColor: {r: 11.98431, g: 0.627451, b: 0.627451, a: 0}
    - _FullGlowDissolveNoiseScale: {r: 0.1, g: 0.1, b: 0, a: 0}
    - _GlitchDistortion: {r: 0.1, g: 0, b: 0, a: 0}
    - _GlitchDistortionScale: {r: 0, g: 3, b: 0, a: 0}
    - _GlitchDistortionSpeed: {r: 0, g: 1, b: 0, a: 0}
    - _GlitchMaskScale: {r: 0, g: 0.2, b: 0, a: 0}
    - _GlitchMaskSpeed: {r: 0, g: 4, b: 0, a: 0}
    - _GlitchNoiseScale: {r: 0, g: 3, b: 0, a: 0}
    - _GlitchNoiseSpeed: {r: 0, g: 1, b: 0, a: 0}
    - _HalftonePosition: {r: 0, g: 0, b: 0, a: 0}
    - _HologramTint: {r: 0.3137255, g: 1.662745, b: 2.996078, a: 1}
    - _IgniteColorInner: {r: 5.992157, g: 2.5, b: 0, a: 1}
    - _IgniteColorOuter: {r: 2.996078, g: 0.5, b: 0, a: 1}
    - _InkSpreadColor: {r: 8.47419, g: 5.013525, b: 0.08873497, a: 0}
    - _InkSpreadNoiseScale: {r: 0.4, g: 0.4, b: 0, a: 0}
    - _InkSpreadPosition: {r: 0.5, g: -1, b: 0, a: 0}
    - _InnerOutlineColor: {r: 11.98431, g: 1.254902, b: 1.254902, a: 1}
    - _InnerOutlineDistortionIntensity: {r: 0.01, g: 0.01, b: 0, a: 0}
    - _InnerOutlineNoiseScale: {r: 4, g: 4, b: 0, a: 0}
    - _InnerOutlineNoiseSpeed: {r: 0, g: 0.1, b: 0, a: 0}
    - _InnerOutlineTextureSpeed: {r: 0.5, g: 0, b: 0, a: 0}
    - _LightningColor: {r: 0.5, g: 0.8, b: 1, a: 1}
    - _LightningOffset: {r: 0.1, g: 0.15, b: 0, a: 0}
    - _MetalColor: {r: 5.992157, g: 3.639216, b: 0.3137255, a: 1}
    - _MetalHighlightColor: {r: 5.992157, g: 3.796078, b: 0.6588235, a: 1}
    - _MetalNoiseDistortion: {r: 0.5, g: 0.5, b: 0, a: 0}
    - _MetalNoiseDistortionScale: {r: 0.2, g: 0.2, b: 0, a: 0}
    - _MetalNoiseDistortionSpeed: {r: -0.05, g: -0.05, b: 0, a: 0}
    - _MetalNoiseScale: {r: 0.25, g: 0.25, b: 0, a: 0}
    - _MetalNoiseSpeed: {r: 0.05, g: 0.05, b: 0, a: 0}
    - _OuterOutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _OuterOutlineDistortionIntensity: {r: 0.01, g: 0.01, b: 0, a: 0}
    - _OuterOutlineNoiseScale: {r: 4, g: 4, b: 0, a: 0}
    - _OuterOutlineNoiseSpeed: {r: 0, g: 0.1, b: 0, a: 0}
    - _OuterOutlineTextureSpeed: {r: 0.5, g: 0, b: 0, a: 0}
    - _PingPongGlowFrom: {r: 5.992157, g: 0.1882353, b: 0.1882353, a: 0}
    - _PingPongGlowTo: {r: 0.1882353, g: 0.1882353, b: 5.992157, a: 0}
    - _PixelOutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _PixelOutlineTextureSpeed: {r: 0.5, g: 0, b: 0, a: 0}
    - _PoisonColor: {r: 2.9960783, g: 0.7302701, b: 0.3294117, a: 0}
    - _PoisonNoiseScale: {r: 0.2, g: 0.2, b: 0, a: 0}
    - _PoisonNoiseSpeed: {r: 0, g: 0.05, b: 0, a: 0}
    - _RainbowCenter: {r: 0, g: 0, b: 0, a: 0}
    - _RainbowNoiseScale: {r: 0.2, g: 0.2, b: 0, a: 0}
    - _RecolorRGBBlueTint: {r: 1, g: 1, b: 1, a: 0.5019608}
    - _RecolorRGBGreenTint: {r: 1, g: 1, b: 1, a: 0.5019608}
    - _RecolorRGBRedTint: {r: 1, g: 1, b: 1, a: 0.5019608}
    - _RecolorRGBYCPBlueTint: {r: 1, g: 1, b: 1, a: 0.5019608}
    - _RecolorRGBYCPCyanTint: {r: 1, g: 1, b: 1, a: 0.5019608}
    - _RecolorRGBYCPGreenTint: {r: 1, g: 1, b: 1, a: 0.5019608}
    - _RecolorRGBYCPPurpleTint: {r: 1, g: 1, b: 1, a: 0.5019608}
    - _RecolorRGBYCPRedTint: {r: 1, g: 1, b: 1, a: 0.5019608}
    - _RecolorRGBYCPYellowTint: {r: 1, g: 1, b: 1, a: 0.5019608}
    - _RendererColor: {r: 1, g: 1, b: 1, a: 1}
    - _ScreenTilingOffset: {r: 0, g: 0, b: 0, a: 0}
    - _ScreenTilingScale: {r: 1, g: 1, b: 0, a: 0}
    - _ShadowColor: {r: 0, g: 0, b: 0, a: 0}
    - _ShadowOffset: {r: 0.05, g: -0.05, b: 0, a: 0}
    - _ShiftingColorA: {r: 1.498039, g: 0, b: 0, a: 0}
    - _ShiftingColorB: {r: 1.498039, g: 0.7490196, b: 0, a: 0}
    - _ShineColor: {r: 11.98431, g: 11.98431, b: 11.98431, a: 0}
    - _SineGlowColor: {r: 0, g: 2.007843, b: 2.996078, a: 0}
    - _SineMoveFrequency: {r: 1, g: 1, b: 0, a: 0}
    - _SineMoveOffset: {r: 0, g: 0.5, b: 0, a: 0}
    - _SineRotatePivot: {r: 0.5, g: 0.5, b: 0, a: 0}
    - _SineScaleFactor: {r: 0.2, g: 0.2, b: 0, a: 0}
    - _SourceAlphaDissolveNoiseScale: {r: 0.3, g: 0.3, b: 0, a: 0}
    - _SourceAlphaDissolvePosition: {r: 0, g: 0, b: 0, a: 0}
    - _SourceGlowDissolveEdgeColor: {r: 11.98431, g: 0.627451, b: 0.627451, a: 0}
    - _SourceGlowDissolveNoiseScale: {r: 0.3, g: 0.3, b: 0, a: 0}
    - _SourceGlowDissolvePosition: {r: 0, g: 0, b: 0, a: 0}
    - _SplitToningHighlightsColor: {r: 1, g: 0.1, b: 0.1, a: 0}
    - _SplitToningShadowsColor: {r: 0.1, g: 0.4000002, b: 1, a: 0}
    - _SpriteSheetRect: {r: 0.75, g: 0, b: 1, a: 1}
    - _SqueezeCenter: {r: 0.5, g: 0.5, b: 0, a: 0}
    - _SqueezeScale: {r: 2, g: 0, b: 0, a: 0}
    - _StrongTintTint: {r: 1, g: 1, b: 1, a: 1}
    - _TextureLayer1Color: {r: 0.4494117, g: 1.298301, b: 2.996079, a: 1}
    - _TextureLayer1Offset: {r: 0, g: 0, b: 0, a: 0}
    - _TextureLayer1Scale: {r: 1, g: 1, b: 0, a: 0}
    - _TextureLayer1ScrollSpeed: {r: 0, g: 1, b: 0, a: 0}
    - _TextureLayer2Color: {r: 0.4494117, g: 1.298301, b: 2.996079, a: 1}
    - _TextureLayer2Offset: {r: 0, g: 0, b: 0, a: 0}
    - _TextureLayer2Scale: {r: 1, g: 1, b: 0, a: 0}
    - _TextureLayer2ScrollSpeed: {r: 0, g: 1, b: 0, a: 0}
    - _UVDistortFrom: {r: -0.02, g: -0.02, b: 0, a: 0}
    - _UVDistortNoiseScale: {r: 0.1, g: 0.1, b: 0, a: 0}
    - _UVDistortSpeed: {r: 2, g: 2, b: 0, a: 0}
    - _UVDistortTo: {r: 0.02, g: 0.02, b: 0, a: 0}
    - _UVRotatePivot: {r: 0.5, g: 0.5, b: 0, a: 0}
    - _UVScalePivot: {r: 0.5, g: 0.5, b: 0, a: 0}
    - _UVScaleScale: {r: 1, g: 1, b: 0, a: 0}
    - _UVScrollSpeed: {r: 0.2, g: 0, b: 0, a: 0}
    - _WorldTilingOffset: {r: 0, g: 0, b: 0, a: 0}
    - _WorldTilingScale: {r: 1, g: 1, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
