using UnityEngine;
using System.Collections.Generic;
using Sirenix.OdinInspector;

public class StatusEffectManager : MonoBehaviour
{
    [Title("Active Effects")]
    [ShowInInspector, ReadOnly]
    private List<StatusEffect> activeEffects = new List<StatusEffect>();
    
    [Title("Debug")]
    [ShowInInspector, ReadOnly]
    private int totalIgniteStacks => CountEffectsOfType(StatusEffectType.Ignite);
    
    private CombatantHealth combatantHealth;
    
    private void Awake()
    {
        combatantHealth = GetComponent<CombatantHealth>();
    }
    
    private void Update()
    {
        if (activeEffects.Count == 0) return;
        
        // Update all effects
        for (int i = activeEffects.Count - 1; i >= 0; i--)
        {
            var effect = activeEffects[i];
            effect.Update(Time.deltaTime);
            
            // Remove expired effects
            if (effect.IsExpired)
            {
                effect.Remove();
                activeEffects.RemoveAt(i);
            }
        }
        
        // Update visual effects
        UpdateVisualEffects();
    }
    
    private void UpdateVisualEffects()
    {
        if (combatantHealth == null) return;

        // Update ignite effect
        int igniteCount = CountEffectsOfType(StatusEffectType.Ignite);
        float igniteIntensity = igniteCount > 0 ? 1f : 0f;
        combatantHealth.SetIgniteAmount(igniteIntensity);

        // Update freeze effect
        int freezeCount = CountEffectsOfType(StatusEffectType.Freeze);
        float freezeIntensity = freezeCount > 0 ? 1f : 0f;
        combatantHealth.SetFrostAmount(freezeIntensity);

        // Update shock effect
        int shockCount = CountEffectsOfType(StatusEffectType.Shock);
        float shockIntensity = shockCount > 0 ? 1f : 0f;
        combatantHealth.SetLightningAmount(shockIntensity);
    }
    
    public void ApplyStatusEffect(StatusEffect effect)
    {
        if (effect == null) return;
        
        // Add the new effect (stacking behavior)
        activeEffects.Add(effect);
        effect.Apply(gameObject);
        
        Debug.Log($"Applied {effect.Type} to {gameObject.name}. Total stacks: {CountEffectsOfType(effect.Type)}");
        
        // Update visual effects immediately
        UpdateVisualEffects();
    }
    
    public void RemoveAllEffectsOfType(StatusEffectType type)
    {
        for (int i = activeEffects.Count - 1; i >= 0; i--)
        {
            if (activeEffects[i].Type == type)
            {
                activeEffects[i].Remove();
                activeEffects.RemoveAt(i);
            }
        }
        
        // Update visual effects
        UpdateVisualEffects();
    }
    
    public void RemoveAllEffects()
    {
        foreach (var effect in activeEffects)
        {
            effect.Remove();
        }
        activeEffects.Clear();
        
        // Clear all visual effects immediately (no transition)
        UpdateVisualEffects();
    }
    
    public int CountEffectsOfType(StatusEffectType type)
    {
        int count = 0;
        foreach (var effect in activeEffects)
        {
            if (effect.Type == type)
                count++;
        }
        return count;
    }
    
    public List<StatusEffect> GetEffectsOfType(StatusEffectType type)
    {
        List<StatusEffect> effects = new List<StatusEffect>();
        foreach (var effect in activeEffects)
        {
            if (effect.Type == type)
                effects.Add(effect);
        }
        return effects;
    }
    
    private void OnDestroy()
    {
        RemoveAllEffects();
    }
    
    // Called when returning to pool
    public void ResetEffects()
    {
        RemoveAllEffects();
    }
    
    // Debug helpers
    [Title("Debug")]
    [Button("Apply Test Ignite")]
    private void DebugApplyIgnite()
    {
        if (Application.isPlaying)
        {
            var ignite = new IgniteEffect(5f, 4f, "Debug");
            ApplyStatusEffect(ignite);
        }
    }
    
    [Button("Apply Test Freeze")]
    private void DebugApplyFreeze()
    {
        if (Application.isPlaying)
        {
            var freeze = new FreezeEffect(0.5f, 3f, "Debug");
            ApplyStatusEffect(freeze);
        }
    }
    
    [Button("Clear All Effects")]
    private void DebugClearEffects()
    {
        if (Application.isPlaying)
        {
            RemoveAllEffects();
        }
    }
}