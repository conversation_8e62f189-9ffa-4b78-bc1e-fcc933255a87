using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using Sirenix.OdinInspector;
using SplatterSystem;

// Is not on Player Object
public class PlayerStats : MonoBehaviour
{
    [Title("Level & Experience")]
    [PropertyRange(1, 100)]
    [OnValueChanged("OnLevelChangedInEditor")]
    public int Level = 1;
    
    [ReadOnly]
    public float currentXP = 0;
    
    [ReadOnly]
    public float xpToNextLevel = 100;
    
    [Title("Health")]
    public float maxHealth = 100f;
    public float currentHealth = 100f;
    public float healthRegen = 0f;
    
    [Title("Mana")]
    public float maxMana = 100f;
    public float currentMana = 100f;
    public float manaRegen = 1f;
    
    [Title("Defensive Stats")]
    public float defense = 0f;
    public float dodgeChance = 0f;
    
    [Title("Movement")]
    public float moveSpeedMultiplier = 1f;
    
    [Title("Offensive Stats")]
    [Tooltip("Damage increased - additive percentage bonus (100 = +100% damage)")]
    [PropertyRange(0, 500)]
    public float damageIncreased = 0f;
    
    [Tooltip("Damage multiplier - multiplicative bonus (1 = normal, 2 = double damage)")]
    [PropertyRange(0.1f, 5f)]
    public float damageMultiplier = 1f;
    
    [Title("Visual Effects")]
    [SerializeField] private bool useHitSplatter = true;
    [Tooltip("Spawn blood splatter when taking damage")]
    [SerializeField] private bool useDeathSplatter = true;
    [Tooltip("Spawn blood splatter when dying")]
    
    [Title("Gem Manager")]
    [ReadOnly]
    [ShowInInspector]
    private GemManager gemManager;
    
    [Title("Events")]
    public UnityEvent<int> OnLevelUp;
    public UnityEvent<float, float> OnXPChanged;
    public UnityEvent<float, float> OnHealthChanged;
    public UnityEvent<float, float> OnManaChanged;
    
    private AbstractSplatterManager splatterManager;
    private float healthRegenTimer = 0f;
    private float manaRegenTimer = 0f;
    
    [Title("Stat System")]
    private StatCalculator statCalculator;
    private PlayerBuffSystem buffSystem;
    private StatModifierCollection allModifiers = new StatModifierCollection();
    private StatusEffectManager statusEffectManager;
    
    private void Start()
    {
        CalculateXPToNextLevel();
        
        // Ensure we have a GemManager component
        gemManager = GetComponent<GemManager>();
        if (gemManager == null)
        {
            gemManager = gameObject.AddComponent<GemManager>();
        }
        
        // Initialize stat system components
        statCalculator = StatCalculator.Instance;
        if (statCalculator == null)
        {
            Debug.LogError("StatCalculator not found! Please add it to the scene.");
        }
        
        buffSystem = GetComponent<PlayerBuffSystem>();
        if (buffSystem == null)
        {
            buffSystem = gameObject.AddComponent<PlayerBuffSystem>();
        }
        
        // Initialize status effect manager
        statusEffectManager = GetComponent<StatusEffectManager>();
        if (statusEffectManager == null)
        {
            statusEffectManager = gameObject.AddComponent<StatusEffectManager>();
        }

        splatterManager = FindFirstObjectByType<BitmapSplatterManager>();

        if(splatterManager == null){
            Debug.LogError("SplatterManager not found");
        }
        
        // Initialize health and mana with calculated values
        currentHealth = GetMaxHealth();
        currentMana = GetMaxMana();
    }
    
    private void Update()
    {
        // Handle health regeneration with stat calculations
        float calculatedHealthRegen = GetCalculatedStat(StatType.HealthRegen);
        if (calculatedHealthRegen > 0 && currentHealth < GetMaxHealth())
        {
            healthRegenTimer += Time.deltaTime;
            if (healthRegenTimer >= 1f)
            {
                Heal(calculatedHealthRegen);
                healthRegenTimer = 0f;
            }
        }
        
        // Handle mana regeneration with stat calculations
        float calculatedManaRegen = GetCalculatedStat(StatType.ManaRegen);
        if (calculatedManaRegen > 0 && currentMana < maxMana)
        {
            manaRegenTimer += Time.deltaTime;
            if (manaRegenTimer >= 1f)
            {
                RestoreMana(calculatedManaRegen);
                manaRegenTimer = 0f;
            }
        }
    }
    
    public void AddXP(float amount)
    {
        // Apply experience gain modifier
        float xpMultiplier = GetCalculatedStat(StatType.ExperienceGain);
        float modifiedAmount = amount * xpMultiplier;
        
        currentXP += modifiedAmount;
        OnXPChanged?.Invoke(currentXP, xpToNextLevel);
        
        while (currentXP >= xpToNextLevel)
        {
            LevelUp();
        }
    }
    
    private void LevelUp()
    {
        currentXP -= xpToNextLevel;
        Level++;
        CalculateXPToNextLevel();
        
        // Small stat boost on level up
        maxHealth += 10f;
        
        // Update current health to new calculated max
        float calculatedMaxHealth = GetMaxHealth();
        currentHealth = calculatedMaxHealth;
        
        OnLevelUp?.Invoke(Level);
        OnXPChanged?.Invoke(currentXP, xpToNextLevel);
        OnHealthChanged?.Invoke(currentHealth, calculatedMaxHealth);
    }
    
    private void CalculateXPToNextLevel()
    {
        // Exponential growth formula
        xpToNextLevel = 100 * Mathf.Pow(1.5f, Level - 1);
    }
    
    
    public void TakeDamage(float damage, string debugInfo = null)
    {
        // Convert to DamageInfo and call the new method
        TakeDamage(new DamageInfo(damage, DamageType.Physical, false, 1f, debugInfo ?? string.Empty));
    }
    
    public void TakeDamage(DamageInfo damageInfo)
    {
        // Apply dodge chance with stat calculations
        float calculatedDodge = GetCalculatedStat(StatType.DodgeChance);
        if (Random.Range(0f, 100f) < calculatedDodge)
        {
            // Dodged!
            Debug.Log($"Dodged {damageInfo.type} damage! (Dodge chance: {calculatedDodge:F1}%)");
            return;
        }
        
        // Apply defense with stat calculations
        float calculatedDefense = GetCalculatedStat(StatType.Defense);
        float finalDamage = Mathf.Max(0.01f, damageInfo.amount - calculatedDefense);
        
        currentHealth = Mathf.Max(0f, currentHealth - finalDamage);
        OnHealthChanged?.Invoke(currentHealth, GetMaxHealth());
        
        if (!string.IsNullOrEmpty(damageInfo.source))
        {
            Debug.Log($"Damage source: {damageInfo.source}");
        }
        Debug.Log($"{damageInfo.type} damage taken: {finalDamage:F2} (Base: {damageInfo.amount}, Defense: {calculatedDefense:F2}){(damageInfo.isCritical ? " [CRITICAL]" : "")}");
        
        // Apply status effects based on damage type using centralized helper
        StatusEffectHelper.TryApplyAilment(damageInfo, finalDamage, statusEffectManager);
        
        // Spawn hit splatter
        if (useHitSplatter)
        {
            
            if (splatterManager != null)
            {
                Debug.Log("Spawning hit splatter");
                splatterManager.Spawn(PlayerManager.PlayerTransform.position);
            }
        }
        
        if (currentHealth <= 0)
        {
            Die();
        }
    }
    
    public void Heal(float amount)
    {
        float calculatedMax = GetMaxHealth();
        currentHealth = Mathf.Min(calculatedMax, currentHealth + amount);
        OnHealthChanged?.Invoke(currentHealth, calculatedMax);
    }
    
    private void Die()
    {
        // Spawn death splatter
        if (useDeathSplatter)
        {
            var splatterManager = FindFirstObjectByType<SplatterSystem.BitmapSplatterManager>();
            if (splatterManager != null)
            {
                splatterManager.Spawn(transform.position);
            }
        }
        
        // Handle player death
        UnityEngine.Debug.Log("Player died!");
    }
    
    // Gem Management
    public GemManager GetGemManager()
    {
        if (gemManager == null)
        {
        gemManager = GetComponent<GemManager>();
        if (gemManager == null)
        {
            gemManager = gameObject.AddComponent<GemManager>();
        }
        }
        return gemManager;
    }
    
    
    public void SpendMana(float amount)
    {
        currentMana = Mathf.Max(0, currentMana - amount);
        OnManaChanged?.Invoke(currentMana, GetMaxMana());
    }
    
    public void RestoreMana(float amount)
    {
        float calculatedMax = GetMaxMana();
        currentMana = Mathf.Min(calculatedMax, currentMana + amount);
        OnManaChanged?.Invoke(currentMana, calculatedMax);
    }
    
    public bool HasEnoughMana(float amount)
    {
        return currentMana >= amount;
    }
    
    // Stat calculation methods
    public float GetCalculatedStat(StatType statType)
    {
        if (statCalculator == null) 
        {
            // Fallback to raw values if stat calculator not available
            return GetRawStatValue(statType);
        }
        
        // Combine modifiers from all sources
        allModifiers.modifiers.Clear();
        
        // Add buff modifiers
        if (buffSystem != null)
        {
            var buffModifiers = buffSystem.GetBuffModifiers();
            allModifiers.modifiers.AddRange(buffModifiers.modifiers);
        }
        
        // Add any other modifier sources here (equipment, temporary effects, etc.)
        
        // Get base value
        float baseValue = GetRawStatValue(statType);
        
        // Calculate with caps and diminishing returns
        return statCalculator.CalculateStat(statType, baseValue, allModifiers);
    }
    
    // Helper methods for commonly accessed calculated stats
    public float GetMaxHealth() => GetCalculatedStat(StatType.MaxHealth);
    public float GetMaxMana() => GetCalculatedStat(StatType.MaxMana);
    
    private float GetRawStatValue(StatType statType)
    {
        switch (statType)
        {
            case StatType.MaxHealth: return maxHealth;
            case StatType.HealthRegen: return healthRegen;
            case StatType.MaxMana: return maxMana;
            case StatType.ManaRegen: return manaRegen;
            case StatType.Defense: return defense;
            case StatType.DodgeChance: return dodgeChance;
            case StatType.MoveSpeed: return moveSpeedMultiplier;
            case StatType.DamageIncreased: return damageIncreased;
            case StatType.DamageMultiplier: return damageMultiplier;
            default: return 0f;
        }
    }
    
    public void RecalculateStats()
    {
        // Force stat recalculation
        // Could fire events here for UI updates
    }
    
    // Editor Debug Tools
    #if UNITY_EDITOR
    [FoldoutGroup("Debug Tools")]
    [PropertySpace]
    [Title("Quick Level Settings")]
    [PropertyRange(1, 100)]
    [InlineButton("SetDebugLevel", "Set")]
    public int debugTargetLevel = 1;
    
    private void SetDebugLevel()
    {
        SetLevel(debugTargetLevel);
    }
    
    [FoldoutGroup("Debug Tools")]
    [ButtonGroup("Debug Tools/LevelButtons")]
    [Button("+1 Level", ButtonSizes.Small)]
    private void AddOneLevel()
    {
        SetLevel(Level + 1);
    }
    
    [ButtonGroup("Debug Tools/LevelButtons")]
    [Button("+5 Levels", ButtonSizes.Small)]
    private void AddFiveLevels()
    {
        SetLevel(Level + 5);
    }
    
    [ButtonGroup("Debug Tools/LevelButtons")]
    [Button("+10 Levels", ButtonSizes.Small)]
    private void AddTenLevels()
    {
        SetLevel(Level + 10);
    }
    
    [FoldoutGroup("Debug Tools")]
    [Button("Force Level Up (with UI)", ButtonSizes.Large)]
    private void ForceLevelUp()
    {
        AddXP(xpToNextLevel);
    }
    
    [FoldoutGroup("Debug Tools")]
    [Button("Add 50 XP", ButtonSizes.Small)]
    private void AddTestXP()
    {
        AddXP(50);
    }
    
    [FoldoutGroup("Debug Tools")]
    [Button("Reset to Level 1", ButtonSizes.Small)]
    [PropertySpace]
    private void ResetLevel()
    {
        SetLevel(1);
        currentXP = 0;
        OnXPChanged?.Invoke(currentXP, xpToNextLevel);
        
        // Reset stats to base
        maxHealth = 100f;
        currentHealth = maxHealth;
        maxMana = 100f;
        currentMana = maxMana;
        moveSpeedMultiplier = 1f;
        damageIncreased = 0f;
        damageMultiplier = 1f;
        defense = 0f;
        dodgeChance = 0f;
        healthRegen = 0f;
        manaRegen = 1f;
        
        OnHealthChanged?.Invoke(currentHealth, maxHealth);
    }
    
    // Set level directly (for debugging)
    private void SetLevel(int newLevel)
    {
        newLevel = Mathf.Clamp(newLevel, 1, 100);
        
        // Calculate stat differences
        int levelDiff = newLevel - Level;
        if (levelDiff > 0)
        {
        // Add health for levels gained
        maxHealth += levelDiff * 10f;
        currentHealth = maxHealth;
        }
        
        Level = newLevel;
        currentXP = 0;
        CalculateXPToNextLevel();
        
        OnLevelUp?.Invoke(Level);
        OnXPChanged?.Invoke(currentXP, xpToNextLevel);
        OnHealthChanged?.Invoke(currentHealth, maxHealth);
    }
    
    // Called when Level is changed in editor
    private void OnLevelChangedInEditor()
    {
        if (Application.isPlaying)
        {
        SetLevel(Level);
        }
    }
    
    [FoldoutGroup("Debug Tools")]
    [Title("Current Stats Overview")]
    [ShowInInspector]
    [PropertySpace]
    private string StatsOverview => 
        $"Health: {currentHealth:F2}/{maxHealth:F2}\n" +
        $"Mana: {currentMana:F2}/{maxMana:F2}\n" +
        $"Move Speed: {moveSpeedMultiplier:F2}x\n" +
        $"Defense: {defense:F2} | Dodge: {dodgeChance:F1}%\n" +
        $"Damage Increased: +{damageIncreased:F0}%\n" +
        $"Damage Multiplier: {damageMultiplier:F2}x";
    
    [FoldoutGroup("Debug Tools")]
    [ProgressBar(0, "xpToNextLevel", ColorGetter = "GetXPBarColor")]
    [ShowInInspector]
    private float XPProgress => currentXP;
    
    private Color GetXPBarColor()
    {
        return Color.Lerp(Color.yellow, Color.green, currentXP / xpToNextLevel);
    }
    #endif
}